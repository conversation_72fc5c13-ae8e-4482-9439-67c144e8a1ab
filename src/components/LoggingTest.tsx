/**
 * Simple test component to verify environment-based logging
 * Add this temporarily to any page to test logging behavior
 */

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { logger, isDevEnvironment, devOnly, sanitizeForLogging } from '@/utils/logger';

const LoggingTest = () => {
  const testBasicLogging = () => {
    console.log('=== TESTING BASIC LOGGING ===');
    console.log('Environment:', import.meta.env.VITE_ENVIRONMENT);
    console.log('Is Dev Environment:', isDevEnvironment());
    
    logger.log('✅ This should appear in dev, hidden in prod');
    logger.error('❌ This error should appear in dev, hidden in prod');
    logger.warn('⚠️ This warning should appear in dev, hidden in prod');
    
    devOnly(() => {
      console.log('🔧 This callback only runs in dev');
    });
    
    const sensitiveData = {
      username: 'testuser',
      password: 'secret123',
      normalData: 'this is fine'
    };
    
    logger.log('Sanitized data:', sanitizeForLogging(sensitiveData));
    console.log('=== END LOGGING TEST ===');
  };

  return (
    <Card className="m-4 border-2 border-yellow-400">
      <CardHeader>
        <CardTitle className="text-yellow-600">
          🧪 Logging Test Component
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="bg-yellow-50 p-3 rounded">
          <p><strong>Environment:</strong> {import.meta.env.VITE_ENVIRONMENT || 'not set'}</p>
          <p><strong>Is Dev:</strong> {isDevEnvironment() ? 'Yes' : 'No'}</p>
        </div>
        
        <Button onClick={testBasicLogging} className="w-full">
          Run Logging Test
        </Button>
        
        <div className="text-sm text-muted-foreground space-y-2">
          <p><strong>Instructions:</strong></p>
          <ol className="list-decimal list-inside space-y-1">
            <li>Click the button above and check browser console</li>
            <li>Change .env: <code>environment=prod</code></li>
            <li>Restart dev server</li>
            <li>Click button again - no logs should appear</li>
            <li>Change back to <code>environment=dev</code></li>
          </ol>
        </div>
        
        <div className="bg-red-50 p-2 rounded text-sm">
          <strong>⚠️ Remove this component before production!</strong>
        </div>
      </CardContent>
    </Card>
  );
};

export default LoggingTest;
