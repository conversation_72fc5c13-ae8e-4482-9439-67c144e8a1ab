import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { MapPin, MessageCircle, UserPlus, UserCheck, Clock, Wifi, WifiOff } from 'lucide-react';

interface NearbyUser {
  id: string;
  name: string;
  avatar_url?: string;
  bio?: string;
  interests: string[];
  latitude: number;
  longitude: number;
  distance_km: number;
  online_status: boolean;
  last_seen: string;
  connection_status: string;
  shared_interests: string[];
}

interface ProfileModalProps {
  user: NearbyUser | null;
  isOpen: boolean;
  onClose: () => void;
  onSendRequest: (userId: string) => void;
  onMessage: (userId: string) => void;
  currentUserId?: string;
}

const ProfileModal: React.FC<ProfileModalProps> = ({
  user,
  isOpen,
  onClose,
  onSendRequest,
  onMessage,
  currentUserId
}) => {
  if (!user) return null;

  const formatDistance = (km: number) => {
    if (km < 1) {
      return `${Math.round(km * 1000)}m away`;
    }
    return `${km.toFixed(1)}km away`;
  };

  const formatLastSeen = (lastSeen: string) => {
    const date = new Date(lastSeen);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const getConnectionButton = () => {
    switch (user.connection_status) {
      case 'accepted':
        return (
          <Button onClick={() => onMessage(user.id)} className="flex-1">
            <MessageCircle className="h-4 w-4 mr-2" />
            Message
          </Button>
        );
      case 'pending':
        return (
          <Button variant="outline" disabled className="flex-1">
            <Clock className="h-4 w-4 mr-2" />
            Request Sent
          </Button>
        );
      case 'none':
      default:
        return (
          <Button onClick={() => onSendRequest(user.id)} className="flex-1">
            <UserPlus className="h-4 w-4 mr-2" />
            Connect
          </Button>
        );
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="sr-only">User Profile</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Profile Header */}
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Avatar className="h-16 w-16">
                <AvatarImage src={user.avatar_url} alt={user.name} />
                <AvatarFallback className="text-lg">
                  {user.name.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              {/* Online status indicator */}
              <div className="absolute -bottom-1 -right-1">
                {user.online_status ? (
                  <div className="h-4 w-4 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                    <Wifi className="h-2 w-2 text-white" />
                  </div>
                ) : (
                  <div className="h-4 w-4 bg-gray-400 rounded-full border-2 border-white flex items-center justify-center">
                    <WifiOff className="h-2 w-2 text-white" />
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex-1">
              <h3 className="text-lg font-semibold">{user.name}</h3>
              <div className="flex items-center text-sm text-muted-foreground">
                <MapPin className="h-3 w-3 mr-1" />
                {formatDistance(user.distance_km)}
              </div>
              <div className="flex items-center text-xs text-muted-foreground mt-1">
                {user.online_status ? (
                  <span className="text-green-600">Online now</span>
                ) : (
                  <span>Last seen {formatLastSeen(user.last_seen)}</span>
                )}
              </div>
            </div>
          </div>

          {/* Bio */}
          {user.bio && (
            <div>
              <h4 className="text-sm font-medium mb-2">About</h4>
              <p className="text-sm text-muted-foreground">{user.bio}</p>
            </div>
          )}

          {/* Interests */}
          {user.interests.length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-2">Interests</h4>
              <div className="flex flex-wrap gap-2">
                {user.interests.map((interest, index) => (
                  <Badge 
                    key={index} 
                    variant={user.shared_interests.includes(interest) ? "default" : "secondary"}
                    className={user.shared_interests.includes(interest) ? "bg-primary/20 text-primary border-primary" : ""}
                  >
                    {interest}
                    {user.shared_interests.includes(interest) && " ✨"}
                  </Badge>
                ))}
              </div>
              {user.shared_interests.length > 0 && (
                <p className="text-xs text-muted-foreground mt-2">
                  ✨ = Shared interests
                </p>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-3">
            {getConnectionButton()}
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ProfileModal;
