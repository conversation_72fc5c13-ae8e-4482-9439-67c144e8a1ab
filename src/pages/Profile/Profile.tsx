import './Profile.css';
import { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { User, ArrowLeft, LogOut, MapPin, Settings, Plus, X, Upload } from 'lucide-react';
import MobileNavigation from '@/components/MobileNavigation';
import { supabase } from '@/integrations/supabase/client';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { toast } from '@/hooks/use-toast';
import { logger, logDbOperation } from '@/utils/logger';

interface ProfileData {
  id: string;
  user_id: string;
  full_name: string;
  username: string;
  bio: string | null;
  interests: string[] | null;
  is_business: boolean | null;
  is_visible: boolean | null;
  visibility_radius: number | null;
  avatar_url?: string | null;
}

const Profile = () => {
  const navigate = useNavigate();
  const { user, loading, signOut } = useAuth();
  const [profile, setProfile] = useState<ProfileData | null>(null);
  const [editing, setEditing] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [formData, setFormData] = useState({
    full_name: '',
    username: '',
    bio: '',
    is_business: false,
    is_visible: true,
    visibility_radius: 5000,
  });
  const [interests, setInterests] = useState<string[]>([]);
  const [newInterest, setNewInterest] = useState('');
  const [saving, setSaving] = useState(false);
  const [usernameError, setUsernameError] = useState<string>('');
  const [checkingUsername, setCheckingUsername] = useState(false);
  const usernameTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!loading && !user) {
      navigate('/auth');
    }
  }, [user, loading, navigate]);

  // Create profile if not exists
  const createProfile = useCallback(async () => {
    try {
      const { error } = await supabase
        .from('profiles')
        .insert({
          user_id: user?.id,
          full_name: user?.user_metadata?.full_name || '',
          username: user?.user_metadata?.username || '',
        });
      if (error) {
        toast({
          title: 'Error creating profile',
          description: error.message,
          variant: 'destructive',
        });
      } else {
        // Will call fetchProfile after this completes
      }
    } catch (error) {
      toast({
        title: 'Error creating profile',
        description: error instanceof Error ? error.message : String(error),
        variant: 'destructive',
      });
    }
  }, [user]);

  // Fetch profile and set state
  const fetchProfile = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', user?.id)
        .single();

      if (error) {
        toast({
          title: 'Error fetching profile',
          description: error.message,
          variant: 'destructive',
        });
        // Create profile if it doesn't exist
        if (error.code === 'PGRST116') {
          await createProfile();
          // Retry fetching after creating profile
          setTimeout(() => fetchProfile(), 1000);
        }
      } else {
        setProfile(data);
        setFormData({
          full_name: data.full_name || '',
          username: data.username || '',
          bio: data.bio || '',
          is_business: data.is_business || false,
          is_visible: data.is_visible ?? true,
          visibility_radius: data.visibility_radius || 5000,
        });
        setInterests(data.interests || []);
        // Prefer profile avatar, else Google avatar, else null
        setAvatarUrl(data.avatar_url || user?.user_metadata?.avatar_url || null);
      }
    } catch (error) {
      toast({
        title: 'Error fetching profile',
        description: error instanceof Error ? error.message : String(error),
        variant: 'destructive',
      });
    }
  }, [user, createProfile]);

  useEffect(() => {
    if (user) {
      fetchProfile();
    }
  }, [user, fetchProfile]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (usernameTimeoutRef.current) {
        clearTimeout(usernameTimeoutRef.current);
      }
    };
  }, []);

  // Handle avatar file upload
  const handleAvatarChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !user) return;
    
    setUploading(true);
    try {
      const fileExt = file.name.split('.').pop();
      const filePath = `${user.id}.${fileExt}`;
      
      // Upload to Supabase Storage
      const { error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(filePath, file, { upsert: true });

      if (uploadError) {
        toast({ 
          title: 'Upload failed', 
          description: uploadError.message, 
          variant: 'destructive' 
        });
        return;
      }

      // Get public URL
      const { data } = supabase.storage
        .from('avatars')
        .getPublicUrl(filePath);

      setAvatarUrl(data.publicUrl);
      toast({ 
        title: 'Avatar updated', 
        description: 'Your avatar has been uploaded successfully.' 
      });
    } catch (error) {
      toast({
        title: 'Upload failed',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive',
      });
    } finally {
      setUploading(false);
    }
  };

  // Check username uniqueness
  const checkUsernameAvailability = useCallback(async (username: string) => {
    if (!username.trim() || username === profile?.username) {
      setUsernameError('');
      return true;
    }

    // Basic validation
    if (username.length < 3) {
      setUsernameError('Username must be at least 3 characters long');
      return false;
    }

    if (username.length > 20) {
      setUsernameError('Username must be no more than 20 characters long');
      return false;
    }

    if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
      setUsernameError('Username can only contain letters, numbers, underscores, and hyphens');
      return false;
    }

    setCheckingUsername(true);
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id')
        .eq('username', username.trim())
        .neq('user_id', user?.id) // Exclude current user
        .maybeSingle();

      if (error) {
        logger.error('Error checking username:', error);
        setUsernameError('Error checking username availability');
        return false;
      }

      if (data) {
        setUsernameError('This username is already taken');
        return false;
      } else {
        setUsernameError('');
        return true;
      }
    } catch (error) {
      logger.error('Error checking username:', error);
      setUsernameError('Error checking username availability');
      return false;
    } finally {
      setCheckingUsername(false);
    }
  }, [user?.id, profile?.username]);

  // Handle username change with validation
  const handleUsernameChange = (newUsername: string) => {
    setFormData({ ...formData, username: newUsername });
    
    // Clear previous error
    setUsernameError('');
    
    // Clear existing timeout
    if (usernameTimeoutRef.current) {
      clearTimeout(usernameTimeoutRef.current);
    }
    
    // Debounce username check
    if (newUsername.trim()) {
      usernameTimeoutRef.current = setTimeout(() => {
        checkUsernameAvailability(newUsername);
      }, 500);
    }
  };

  const saveProfile = async () => {
    // Validate username before saving
    const isUsernameValid = await checkUsernameAvailability(formData.username);
    
    if (!isUsernameValid || usernameError) {
      toast({
        title: "Cannot save profile",
        description: usernameError || "Please choose a different username",
        variant: "destructive",
      });
      return;
    }

    setSaving(true);
    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          ...formData,
          interests: interests.length > 0 ? interests : null,
          avatar_url: avatarUrl,
        })
        .eq('user_id', user?.id);

      if (error) {
        logDbOperation('UPDATE', 'profiles', { error: error.message });
        toast({
          title: "Error",
          description: "Failed to save profile",
          variant: "destructive",
        });
      } else {
        logDbOperation('UPDATE', 'profiles', { status: 'success' });
        toast({
          title: "Profile Updated",
          description: "Your profile has been saved successfully",
        });
        setEditing(false);
        fetchProfile();
      }
    } catch (error) {
      logger.error('Error saving profile:', error);
    } finally {
      setSaving(false);
    }
  };

  const addInterest = () => {
    if (newInterest.trim() && !interests.includes(newInterest.trim())) {
      setInterests([...interests, newInterest.trim()]);
      setNewInterest('');
    }
  };

  const removeInterest = (interest: string) => {
    setInterests(interests.filter(i => i !== interest));
  };

  const handleSignOut = async () => {
    await signOut();
    navigate('/auth');
  };

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <User className="h-12 w-12 text-primary mx-auto pulse-location" />
          <p className="text-muted-foreground">Loading profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background pb-20 mobile-safe-area">
      {/* Header */}
      <header className="bg-card border-b border-border sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Button 
                variant="ghost" 
                size="icon"
                onClick={() => navigate('/')}
                className="md:hidden"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <User className="h-6 w-6 text-primary" />
              <h1 className="text-xl font-bold">Profile</h1>
            </div>
            <div className="flex items-center space-x-2">
              {editing ? (
                <>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => {
                      setEditing(false);
                      setUsernameError('');
                      setCheckingUsername(false);
                    }}
                    disabled={saving}
                  >
                    Cancel
                  </Button>
                  <Button 
                    size="sm"
                    onClick={saveProfile}
                    disabled={saving || !!usernameError || checkingUsername}
                  >
                    {saving ? 'Saving...' : 'Save'}
                  </Button>
                </>
              ) : (
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    setEditing(true);
                    setUsernameError('');
                    setCheckingUsername(false);
                  }}
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="profile-main space-y-2">
        {/* Profile Card */}
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto h-20 w-20 flex flex-col items-center justify-center mb-4 relative">
              <Avatar className="h-20 w-20">
                {avatarUrl ? (
                  <AvatarImage src={avatarUrl} alt="Profile avatar" />
                ) : (
                  <AvatarFallback className="text-2xl font-bold">
                    {formData.full_name.charAt(0) || 'U'}
                  </AvatarFallback>
                )}
              </Avatar>
              {editing && (
                <>
                  <input
                    type="file"
                    accept="image/*"
                    ref={fileInputRef}
                    style={{ display: 'none' }}
                    onChange={handleAvatarChange}
                  />
                  <Button
                    type="button"
                    size="icon"
                    variant="outline"
                    className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full shadow-lg bg-white hover:bg-gray-50"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={uploading}
                  >
                    {uploading ? (
                      <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full" />
                    ) : (
                      <Upload className="h-4 w-4" />
                    )}
                  </Button>
                </>
              )}
            </div>
            <CardTitle className="text-xl">
              {editing ? (
                <Input
                  value={formData.full_name}
                  onChange={(e) => setFormData({ ...formData, full_name: e.target.value })}
                  placeholder="Full Name"
                />
              ) : (
                formData.full_name || 'Unknown User'
              )}
            </CardTitle>
            <p className="text-muted-foreground">
              {editing ? (
                <div className="space-y-1">
                  <Input
                    value={formData.username}
                    onChange={(e) => handleUsernameChange(e.target.value)}
                    placeholder="username"
                    className={`text-center ${usernameError ? 'border-red-500 focus:border-red-500' : ''}`}
                    disabled={checkingUsername}
                  />
                  {checkingUsername && (
                    <p className="text-xs text-muted-foreground text-center">
                      Checking availability...
                    </p>
                  )}
                  {usernameError && (
                    <p className="text-xs text-red-500 text-center">
                      {usernameError}
                    </p>
                  )}
                  {!usernameError && !checkingUsername && formData.username && formData.username !== profile?.username && (
                    <p className="text-xs text-green-600 text-center">
                      Username is available
                    </p>
                  )}
                </div>
              ) : (
                `@${formData.username || 'user'}`
              )}
            </p>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {editing ? (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="bio">Bio</Label>
                  <Textarea
                    id="bio"
                    value={formData.bio}
                    onChange={(e) => setFormData({ ...formData, bio: e.target.value })}
                    placeholder="Tell people about yourself..."
                    rows={3}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="is_business">Business Account</Label>
                  <Switch
                    id="is_business"
                    checked={formData.is_business}
                    onCheckedChange={(checked) => setFormData({ ...formData, is_business: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="is_visible">Profile Visible to Others</Label>
                  <Switch
                    id="is_visible"
                    checked={formData.is_visible}
                    onCheckedChange={(checked) => setFormData({ ...formData, is_visible: checked })}
                  />
                </div>

                <div>
                  <Label htmlFor="radius">Visibility Radius (meters)</Label>
                  <Input
                    id="radius"
                    type="number"
                    min="100"
                    max="50000"
                    value={formData.visibility_radius}
                    onChange={(e) => setFormData({ ...formData, visibility_radius: Number(e.target.value) })}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    How far others can see your profile and posts
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                {formData.bio && (
                  <p className="text-center text-muted-foreground">{formData.bio}</p>
                )}
                
                <div className="flex justify-center space-x-4 text-sm">
                  {formData.is_business && (
                    <Badge variant="secondary">Business</Badge>
                  )}
                  <div className="flex items-center space-x-1 text-muted-foreground">
                    <MapPin className="h-3 w-3" />
                    <span>{formData.visibility_radius}m radius</span>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Interests */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Interests</CardTitle>
          </CardHeader>
          <CardContent>
            {editing && (
              <div className="flex space-x-2 mb-4">
                <Input
                  value={newInterest}
                  onChange={(e) => setNewInterest(e.target.value)}
                  placeholder="Add an interest..."
                  onKeyPress={(e) => e.key === 'Enter' && addInterest()}
                />
                <Button onClick={addInterest} size="icon">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            )}
            
            {interests.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {interests.map((interest, index) => (
                  <Badge key={index} variant="secondary" className="flex items-center space-x-1">
                    <span>{interest}</span>
                    {editing && (
                      <X 
                        className="h-3 w-3 cursor-pointer" 
                        onClick={() => removeInterest(interest)}
                      />
                    )}
                  </Badge>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground text-center py-4">
                {editing ? 'Add some interests to help others find you!' : 'No interests added yet'}
              </p>
            )}
          </CardContent>
        </Card>

        {/* Account Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Account</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <p className="text-sm font-medium">Email</p>
              <p className="text-sm text-muted-foreground">{user.email}</p>
            </div>
            
            <Separator />
            
            <Button 
              variant="destructive" 
              onClick={handleSignOut}
              className="w-full"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Sign Out
            </Button>
          </CardContent>
        </Card>
      </main>

      <MobileNavigation />
    </div>
  );
};

export default Profile;
