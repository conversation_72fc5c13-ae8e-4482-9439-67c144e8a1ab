import './Nearby.css';
import { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MapPin, MessageCircle, ArrowLeft, Users, Search, Filter, Wifi, WifiOff } from 'lucide-react';
import MobileNavigation from '@/components/MobileNavigation';
import ProfileModal from '@/components/ProfileModal';
import DebugLocationInfo from '@/components/DebugLocationInfo';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { logger, logDbOperation, isDevEnvironment } from '@/utils/logger';
import { GoogleMap, Marker, useLoadScript, OverlayView, Circle } from '@react-google-maps/api';
import { MarkerClusterer } from '@react-google-maps/api';
import { getUserLocation } from '@/hooks/useUserLocation';
import { testNearbyConnectFeatures, debugNearbyConnectSetup, safeRpcCall, validateCoordinates, validateRadius } from '@/utils/nearbyTestUtils';

const mapContainerStyle = { width: '100vw', height: '70vh' };

const customMapStyle = [
  // Hide roads (keep minimal map styling)
  { featureType: "road", elementType: "geometry", stylers: [{ visibility: "on" }] },
  { featureType: "road", elementType: "labels", stylers: [{ visibility: "on" }] },
  // Show businesses and POI icons
  { featureType: "poi.business", stylers: [{ visibility: "off" }] },
  // Keep transit hidden
  { featureType: "transit", stylers: [{ visibility: "off" }] },
  // Show place names and icons
  { featureType: "poi", elementType: "labels.text", stylers: [{ visibility: "off" }] },
  { featureType: "poi", elementType: "labels.icon", stylers: [{ visibility: "off" }] },
  { featureType: "administrative", elementType: "labels.text", stylers: [{ visibility: "on" }] },
];

const defaultCenter = {
  lat: 28.6139, // Delhi as fallback
  lng: 77.2090
};




type NearbyUser = {
  id: string;
  name: string;
  avatar_url?: string;
  bio?: string;
  interests: string[];
  latitude: number;
  longitude: number;
  distance_km: number;
  online_status: boolean;
  last_seen: string;
  connection_status: string;
  shared_interests: string[];
};


const Nearby = () => {
  // Track if initial location has been set to prevent effect from running after manual updates
  const initialLocationSetRef = useRef(false);
  const navigate = useNavigate();
  const { user, loading } = useAuth();
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [nearbyUsers, setNearbyUsers] = useState<NearbyUser[]>([]);
  const [loadingNearby, setLoadingNearby] = useState(false);
  const [selectedUser, setSelectedUser] = useState<NearbyUser | null>(null);
  const [showProfileModal, setShowProfileModal] = useState(false);

  // Discovery settings
  const [radiusKm, setRadiusKm] = useState(2); // kilometers
  const [interestFilter, setInterestFilter] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // Map settings
  const [accuracy, setAccuracy] = useState(30); // default 30m
  const [mapZoom, setMapZoom] = useState(15);
  const [mapKey, setMapKey] = useState(0);

  // Real-time subscriptions
  const subscriptionRef = useRef<any>(null);


  const { isLoaded: mapsLoaded } = useLoadScript({
    googleMapsApiKey: import.meta.env.VITE_GOOGLE_MAPS_API_KEY,
  });



  // Update user location using new RPC with validation
  const updateUserLocation = useCallback(async (lat: number, lng: number) => {
    if (!user) return false;

    // Validate coordinates
    if (!validateCoordinates(lat, lng)) {
      toast({
        title: 'Invalid Location',
        description: 'The provided coordinates are invalid.',
        variant: 'destructive'
      });
      return false;
    }

    const result = await safeRpcCall('update_user_location', {
      lat,
      lng,
      accuracy
    });

    if (result.success) {
      logger.log('✅ Location updated successfully:', result.data);
      return true;
    } else {
      logger.error('❌ Failed to update location:', result.error);
      toast({
        title: 'Location Update Failed',
        description: result.error,
        variant: 'destructive'
      });
      return false;
    }
  }, [user, accuracy]);

  // Fetch nearby users using enhanced RPC with validation
  const fetchNearbyUsers = useCallback(async (lat: number, lng: number) => {
    setLoadingNearby(true);
    logger.log(`🔍 Searching for nearby users at [${lat}, ${lng}] with radius ${radiusKm}km`);

    // Validate inputs
    if (!validateCoordinates(lat, lng)) {
      toast({
        title: 'Invalid Location',
        description: 'Cannot search with invalid coordinates.',
        variant: 'destructive'
      });
      setLoadingNearby(false);
      return;
    }

    if (!validateRadius(radiusKm)) {
      toast({
        title: 'Invalid Radius',
        description: 'Search radius must be between 1 and 100 kilometers.',
        variant: 'destructive'
      });
      setLoadingNearby(false);
      return;
    }

    // First, update our own location
    await updateUserLocation(lat, lng);

    // Then search for nearby users with filters
    const result = await safeRpcCall('find_nearby_users', {
      user_lat: lat,
      user_lng: lng,
      radius_km: radiusKm,
      interest_filter: interestFilter.length > 0 ? interestFilter : null,
      include_offline: true
    });

    if (result.success) {
      logger.log(`✅ Found ${result.data?.length || 0} nearby users`);

      const mappedUsers: NearbyUser[] = (result.data || []).map((u: any) => ({
        id: u.id,
        name: u.name || 'Unknown User',
        avatar_url: u.avatar_url,
        bio: u.bio,
        interests: u.interests || [],
        latitude: u.latitude,
        longitude: u.longitude,
        distance_km: u.distance_km,
        online_status: u.online_status,
        last_seen: u.last_seen,
        connection_status: u.connection_status || 'none',
        shared_interests: u.shared_interests || []
      }));

      setNearbyUsers(mappedUsers);
    } else {
      logger.error('❌ Error finding nearby users:', result.error);
      toast({
        title: 'Search Failed',
        description: result.error,
        variant: 'destructive'
      });
      setNearbyUsers([]);
    }
    setLoadingNearby(false);
  }, [radiusKm, interestFilter, updateUserLocation]);



  // Real-time subscription for location updates
  const setupRealtimeSubscription = useCallback(() => {
    if (!userLocation || !user) return;

    // Clean up existing subscription
    if (subscriptionRef.current) {
      subscriptionRef.current.unsubscribe();
    }

    // Subscribe to profiles table changes for nearby users
    subscriptionRef.current = supabase
      .channel('nearby-users')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'profiles',
          filter: `latitude=neq.null,longitude=neq.null,visibility_status=eq.visible`
        },
        (payload) => {
          logger.log('📡 Real-time profile update:', payload);
          // Refresh nearby users when someone updates their location
          if (userLocation) {
            fetchNearbyUsers(userLocation.lat, userLocation.lng);
          }
        }
      )
      .subscribe();

    logger.log('📡 Real-time subscription setup for nearby users');
  }, [userLocation, user, fetchNearbyUsers]);

  // Send connection request
  const handleSendConnectionRequest = useCallback(async (receiverId: string) => {
    if (!user) {
      toast({ title: 'Sign in required', description: 'Please sign in to connect with users.', variant: 'destructive' });
      navigate('/auth');
      return;
    }

    try {
      const { error } = await supabase.from('connections').insert({
        requester_id: user.id,
        receiver_id: receiverId,
        status: 'pending',
      });

      if (error) throw error;

      toast({ title: 'Request Sent', description: 'Connection request sent successfully!' });

      // Update the user's connection status locally
      setNearbyUsers(prev => prev.map(u =>
        u.id === receiverId ? { ...u, connection_status: 'pending' } : u
      ));

    } catch (e) {
      logger.error('❌ Failed to send connection request:', e);
      toast({ title: 'Error', description: 'Could not send connection request.', variant: 'destructive' });
    }
  }, [user, navigate]);

  // Handle messaging
  const handleMessage = useCallback(async (partnerId: string) => {
    if (!user) {
      toast({ title: 'Sign in required', description: 'Please sign in to message users.', variant: 'destructive' });
      navigate('/auth');
      return;
    }

    try {
      const { data, error } = await supabase.rpc('get_or_create_conversation', { partner_id: partnerId });
      if (error) throw error;
      navigate(`/messages?c=${data}&u=${partnerId}`);
    } catch (e) {
      toast({ title: 'Connect first', description: 'Send a connection request before messaging.', variant: 'destructive' });
    }
  }, [user, navigate]);

  // Filter users by search query (name or interests)
  const filteredNearbyUsers = nearbyUsers.filter(user => {
    if (!searchQuery.trim()) return true;

    const query = searchQuery.toLowerCase();
    const nameMatch = user.name.toLowerCase().includes(query);
    const interestMatch = user.interests.some(interest =>
      interest.toLowerCase().includes(query)
    );

    return nameMatch || interestMatch;
  });

  // Get all unique interests from nearby users for filter options
  const allInterests = Array.from(new Set(nearbyUsers.flatMap(u => u.interests || [])));

  // Handle marker click
  const handleMarkerClick = useCallback((user: NearbyUser) => {
    setSelectedUser(user);
    setShowProfileModal(true);
  }, []);

  // Update online presence
  const updateOnlinePresence = useCallback(async () => {
    if (!user) return;

    const result = await safeRpcCall('update_online_presence', { is_online: true });

    if (result.success) {
      logger.log('✅ Online presence updated');
    } else {
      logger.error('❌ Failed to update online presence:', result.error);
    }
  }, [user]);

  useEffect(() => {
    if (!loading && !user) {
      navigate('/auth');
    }
  }, [user, loading, navigate]);

  // Manual location fetch handler
  const handleLocateMe = async () => {
    const loc = await getUserLocation();
    if (loc) {
      setUserLocation(loc);
      setMapZoom(17);
      setMapKey(prevKey => prevKey + 1);
      await updateUserLocation(loc.lat, loc.lng);
      await fetchNearbyUsers(loc.lat, loc.lng);
      await updateOnlinePresence();
      setAccuracy(30);
      toast({ title: 'Location Updated', description: 'Your location has been updated.', variant: 'default' });
    } else {
      toast({ title: 'Location Error', description: 'Unable to fetch your location', variant: 'destructive' });
    }
  };

  // Initialize location and setup real-time subscriptions
  useEffect(() => {
    if (user && !userLocation && !initialLocationSetRef.current) {
      (async () => {
        const loc = await getUserLocation();
        if (loc) {
          setUserLocation(loc);
          await updateUserLocation(loc.lat, loc.lng);
          await fetchNearbyUsers(loc.lat, loc.lng);
          await updateOnlinePresence();
          initialLocationSetRef.current = true;
        } else {
          toast({ title: 'Location Error', description: 'Unable to fetch your location', variant: 'destructive' });
        }
      })();
    }
  }, [user, updateUserLocation, fetchNearbyUsers, updateOnlinePresence]);

  // Setup real-time subscriptions when location is available
  useEffect(() => {
    if (userLocation && user) {
      setupRealtimeSubscription();
    }

    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
      }
    };
  }, [userLocation, user, setupRealtimeSubscription]);

  // Reset state when user logs out
  useEffect(() => {
    if (!user) {
      initialLocationSetRef.current = false;
      setUserLocation(null);
      setNearbyUsers([]);
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
      }
    }
  }, [user]);

  // Refresh nearby users when filters change
  useEffect(() => {
    if (userLocation) {
      fetchNearbyUsers(userLocation.lat, userLocation.lng);
    }
  }, [radiusKm, interestFilter, fetchNearbyUsers, userLocation]);

  // Add debug logging to help diagnose map rendering issues
  useEffect(() => {
    logger.log('mapsLoaded:', mapsLoaded, 'userLocation:', userLocation);
  }, [mapsLoaded, userLocation]);

  return (
    <div className="nearby-root-container min-h-screen bg-background pb-20 mobile-safe-area">
      {/* Header */}
      <header className="nearby-header bg-card border-b border-border sticky top-0 z-40">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => navigate('/')}
                className="md:hidden"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <MapPin className="h-6 w-6 text-primary" />
              <h1 className="text-lg font-bold">Nearby Connect</h1>
            </div>
            <div className="flex items-center space-x-2">
              {/* Debug button for development */}
              {process.env.NODE_ENV === 'development' && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={async () => {
                    await testNearbyConnectFeatures();
                    await debugNearbyConnectSetup();
                  }}
                  className="text-xs"
                >
                  🧪 Test
                </Button>
              )}
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-5 w-5" />
              </Button>
              <div className="flex items-center text-sm text-muted-foreground">
                <Users className="h-4 w-4 mr-1" />
                {nearbyUsers.length}
              </div>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="mt-3 space-y-3">
            <div className="flex space-x-2">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by name or interests..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={radiusKm.toString()} onValueChange={(value) => setRadiusKm(parseInt(value))}>
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1km</SelectItem>
                  <SelectItem value="2">2km</SelectItem>
                  <SelectItem value="5">5km</SelectItem>
                  <SelectItem value="10">10km</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {showFilters && allInterests.length > 0 && (
              <div className="space-y-2">
                <p className="text-sm font-medium">Filter by interests:</p>
                <div className="flex flex-wrap gap-2">
                  {allInterests.slice(0, 10).map((interest) => (
                    <Badge
                      key={interest}
                      variant={interestFilter.includes(interest) ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => {
                        setInterestFilter(prev =>
                          prev.includes(interest)
                            ? prev.filter(i => i !== interest)
                            : [...prev, interest]
                        );
                      }}
                    >
                      {interest}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="nearby-main">
        {/* Google Map for Nearby Users */}
        {mapsLoaded && userLocation ? (
          <div className="nearby-map overflow-hidden relative">
            {/* Loading overlay */}
            {loadingNearby && (
              <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-30 bg-white/90 rounded-lg px-4 py-2 shadow-lg">
                <div className="flex items-center space-x-2">
                  <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full"></div>
                  <span className="text-sm">Finding nearby users...</span>
                </div>
              </div>
            )}

            {/* Locate me button */}
            <div className="absolute bottom-6 right-3 z-30">
              <Button onClick={handleLocateMe} variant="outline" className="locate-me-button shadow-lg">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="7"></circle>
                  <line x1="12" y1="1" x2="12" y2="4"></line>
                  <line x1="12" y1="20" x2="12" y2="23"></line>
                  <line x1="1" y1="12" x2="4" y2="12"></line>
                  <line x1="20" y1="12" x2="23" y2="12"></line>
                </svg>
              </Button>
            </div>

            {/* User count overlay */}
            <div className="absolute top-4 left-4 z-30 bg-white/90 rounded-lg px-3 py-2 shadow-lg">
              <div className="flex items-center space-x-2 text-sm">
                <Users className="h-4 w-4 text-primary" />
                <span className="font-medium">{filteredNearbyUsers.length} nearby</span>
              </div>
            </div>
            <GoogleMap
              key={mapKey}
              mapContainerStyle={mapContainerStyle}
              zoom={mapZoom}
              center={userLocation}
              options={{
                styles: customMapStyle,
                disableDefaultUI: true,
                zoomControl: false,
                streetViewControl: false,
                mapTypeControl: false,
                clickableIcons: false,
              }}
            >
              {/* User location accuracy circle */}
              <Circle
                center={userLocation}
                radius={accuracy}
                options={{
                  strokeColor: '#4285f4',
                  strokeOpacity: 0.3,
                  strokeWeight: 1,
                  fillColor: '#4285f4',
                  fillOpacity: 0.12,
                  clickable: false,
                  draggable: false,
                  editable: false,
                  zIndex: 99,
                }}
              />

              {/* Pulsing blue dot for user location */}
              <OverlayView position={userLocation} mapPaneName={OverlayView.OVERLAY_MOUSE_TARGET}>
                <div className="pulse-dot" />
              </OverlayView>

              {/* Discovery radius circle */}
              <Circle
                center={userLocation}
                radius={radiusKm * 1000} // Convert km to meters
                options={{
                  strokeColor: '#3366FF',
                  strokeOpacity: 0.7,
                  strokeWeight: 2,
                  fillColor: '#3366FF',
                  fillOpacity: 0.1,
                  clickable: false,
                  draggable: false,
                  editable: false,
                }}
              />

              {/* User markers with clustering */}
              <MarkerClusterer
                options={{
                  styles: [
                    {
                      textColor: '#fff',
                      url: 'data:image/svg+xml;base64,' + btoa(`
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">
                          <circle cx="20" cy="20" r="18" fill="#3366FF" stroke="#fff" stroke-width="2"/>
                          <text x="20" y="26" text-anchor="middle" fill="white" font-size="14" font-weight="bold">TEXT_PLACEHOLDER</text>
                        </svg>
                      `),
                      width: 40,
                      height: 40,
                      textSize: 14,
                    },
                  ],
                }}
              >
                {(clusterer) => (
                  <>
                    {filteredNearbyUsers.map(user => (
                      <OverlayView
                        key={user.id}
                        position={{ lat: user.latitude, lng: user.longitude }}
                        mapPaneName={OverlayView.OVERLAY_MOUSE_TARGET}
                      >
                        <div
                          className="relative cursor-pointer transform -translate-x-1/2 -translate-y-1/2"
                          onClick={() => handleMarkerClick(user)}
                        >
                          {/* Avatar marker */}
                          <div className="relative">
                            <div className="w-12 h-12 rounded-full border-3 border-white shadow-lg overflow-hidden bg-white">
                              <img
                                src={user.avatar_url || '/icons/user-avatar.svg'}
                                alt={user.name}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  (e.target as HTMLImageElement).src = '/icons/user-avatar.svg';
                                }}
                              />
                            </div>
                            {/* Online status indicator */}
                            <div className="absolute -bottom-1 -right-1">
                              {user.online_status ? (
                                <div className="w-4 h-4 bg-green-500 rounded-full border-2 border-white">
                                  <Wifi className="w-2 h-2 text-white m-0.5" />
                                </div>
                              ) : (
                                <div className="w-4 h-4 bg-gray-400 rounded-full border-2 border-white">
                                  <WifiOff className="w-2 h-2 text-white m-0.5" />
                                </div>
                              )}
                            </div>
                            {/* Connection status indicator */}
                            {user.connection_status === 'accepted' && (
                              <div className="absolute -top-1 -right-1">
                                <div className="w-4 h-4 bg-blue-500 rounded-full border-2 border-white flex items-center justify-center">
                                  <MessageCircle className="w-2 h-2 text-white" />
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </OverlayView>
                    ))}
                  </>
                )}
              </MarkerClusterer>
            </GoogleMap>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-12">
            <MapPin className="h-10 w-10 text-muted-foreground mb-2" />
            <p className="text-muted-foreground">Unable to load map. Please enable location services and refresh.</p>
          </div>
        )}

        {/* Nearby Users List */}
        {filteredNearbyUsers.length > 0 && (
          <div className="p-4 space-y-4">
            <h2 className="text-lg font-semibold">People Nearby ({filteredNearbyUsers.length})</h2>
            <div className="grid grid-cols-2 gap-3">
              {filteredNearbyUsers.slice(0, 6).map((user) => (
                <Card
                  key={user.id}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => handleMarkerClick(user)}
                >
                  <CardContent className="p-3">
                    <div className="flex items-center space-x-3">
                      <div className="relative">
                        <div className="w-10 h-10 rounded-full overflow-hidden bg-muted">
                          <img
                            src={user.avatar_url || '/icons/user-avatar.svg'}
                            alt={user.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        {user.online_status && (
                          <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-sm truncate">{user.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {user.distance_km < 1
                            ? `${Math.round(user.distance_km * 1000)}m away`
                            : `${user.distance_km.toFixed(1)}km away`
                          }
                        </p>
                        {user.shared_interests.length > 0 && (
                          <p className="text-xs text-primary">
                            {user.shared_interests.length} shared interest{user.shared_interests.length > 1 ? 's' : ''}
                          </p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
            {filteredNearbyUsers.length > 6 && (
              <p className="text-center text-sm text-muted-foreground">
                +{filteredNearbyUsers.length - 6} more on the map
              </p>
            )}
          </div>
        )}

        {/* Profile Modal */}
        <ProfileModal
          user={selectedUser}
          isOpen={showProfileModal}
          onClose={() => {
            setShowProfileModal(false);
            setSelectedUser(null);
          }}
          onSendRequest={handleSendConnectionRequest}
          onMessage={handleMessage}
          currentUserId={user?.id}
        />

        {/* Debug Information - Only show this when env is dev */}
        {
          isDevEnvironment() && (
            <div className="mt-8">
              <DebugLocationInfo />
            </div>
          )
        }
      </main>

      <MobileNavigation />
    </div>
  );
};

export default Nearby;