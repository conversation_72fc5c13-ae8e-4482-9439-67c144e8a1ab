.nearby-root-container {
    /* Ensure full height and proper mobile handling */
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height for mobile */
    background-color: var(--background);
    padding-bottom: env(safe-area-inset-bottom);
}

header.nearby-header {
    width: 100%;
    position: sticky;
    top: 0;
    z-index: 40;
    backdrop-filter: blur(8px);
}

main.nearby-main {
    padding-bottom: 5rem; /* Space for mobile navigation */
}

.nearby-map {
    /* Enhanced map container for discovery */
    height: 70vh;
    min-height: 400px;
    overflow: hidden;
    position: relative;
    border-bottom: 1px solid hsl(var(--border));
}

.card-no-posts {
    padding: 1rem !important;
    text-align: center;
}

.card-no-posts p {
    margin-bottom: .75rem;
}

button.locate-me-button {
    border-radius: 50%;
    width: 3rem;
    height: 3rem;
    padding: 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(8px);
}

/* Enhanced pulsing blue dot for user location */
.pulse-dot {
    width: 20px;
    height: 20px;
    background: #4285f4;
    border: 4px solid white;
    border-radius: 50%;
    box-shadow: 0 2px 12px rgba(66, 133, 244, 0.4);
    animation: pulse 2s infinite;
    position: relative;
    z-index: 100;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 2px 12px rgba(66, 133, 244, 0.4);
    }
    50% {
        transform: scale(1.3);
        box-shadow: 0 4px 20px rgba(66, 133, 244, 0.6);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 2px 12px rgba(66, 133, 244, 0.4);
    }
}

/* User marker animations */
.user-marker {
    transition: transform 0.3s ease;
    cursor: pointer;
}

.user-marker:hover {
    transform: scale(1.1);
}

/* Interest badges */
.interest-badge {
    transition: all 0.2s ease;
}

.interest-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Shared interest highlight */
.shared-interest {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)) 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.shared-interest::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Loading states */
.loading-overlay {
    backdrop-filter: blur(4px);
    background: rgba(255, 255, 255, 0.9);
}

/* Mobile-specific adjustments */
@media (max-width: 768px) {
    .nearby-map {
        height: 65vh;
        min-height: 350px;
    }

    button.locate-me-button {
        width: 2.75rem;
        height: 2.75rem;
    }

    .pulse-dot {
        width: 16px;
        height: 16px;
        border-width: 3px;
    }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
    .loading-overlay {
        background: rgba(0, 0, 0, 0.8);
    }
}
