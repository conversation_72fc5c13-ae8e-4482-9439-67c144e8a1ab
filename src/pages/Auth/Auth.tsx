import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { MapPin, Users, MessageCircle } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { toast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { Capacitor } from '@capacitor/core';
import Logo from "@/components/Logo";

const Auth = () => {
  const navigate = useNavigate();
  const { signUp, signIn, user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [signupForm, setSignupForm] = useState({
    fullName: '',
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [signupErrors, setSignupErrors] = useState({
    passwordMatch: true,
    usernameUnique: true,
  });

  // Realtime password confirmation validation
  useEffect(() => {
    setSignupErrors((prev) => ({
      ...prev,
      passwordMatch: signupForm.password === signupForm.confirmPassword,
    }));
  }, [signupForm.password, signupForm.confirmPassword]);

  // Realtime username uniqueness & suggestion
  useEffect(() => {
    const checkUsername = async () => {
      if (signupForm.username.length < 3) return;
      const { data } = await supabase
        .from('profiles')
        .select('username')
        .eq('username', signupForm.username)
        .single();
      if (data) {
        // Instead of auto-changing, show error and prompt user
        setSignupErrors((prev) => ({
          ...prev,
          usernameUnique: false,
        }));
        toast({
          title: 'Username taken',
          description: 'Please choose a different username.',
          variant: 'destructive',
        });
      } else {
        setSignupErrors((prev) => ({
          ...prev,
          usernameUnique: true,
        }));
      }
    };
    checkUsername();
  }, [signupForm.username]);

  // Auto-suggest username from email if blank
  useEffect(() => {
    if (signupForm.email && signupForm.username === '') {
      const base = signupForm.email.split('@')[0].replace(/[^a-zA-Z0-9]/g, '');
      const suffix = Math.floor(1000 + Math.random() * 9000);
      setSignupForm((prev) => ({
        ...prev,
        username: `${base}${suffix}`,
      }));
    }
  }, [signupForm.email, signupForm.username]);

  const handleSignupInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSignupForm({ ...signupForm, [e.target.name]: e.target.value });
  };

  const handleSignUp = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    if (!signupErrors.passwordMatch) {
      toast({ title: 'Error', description: 'Passwords do not match.', variant: 'destructive' });
      setLoading(false);
      return;
    }
    if (!signupErrors.usernameUnique) {
      toast({ title: 'Error', description: 'Username already taken.', variant: 'destructive' });
      setLoading(false);
      return;
    }

    const { error } = await signUp(
      signupForm.email,
      signupForm.password,
      signupForm.fullName,
      signupForm.username
    );

    if (error) {
      if (error.message && error.message.toLowerCase().includes('already registered')) {
        toast({
          title: "Sign Up Failed",
          description: "Email already in use.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Sign Up Failed",
          description: error.message,
          variant: "destructive",
        });
      }
    } else {
      toast({
        title: "Welcome to Nearby Connect!",
        description: "Please check your email to verify your account.",
      });
    }

    setLoading(false);
  };

  const handleSignIn = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);

    const formData = new FormData(e.currentTarget);
    const email = formData.get('email') as string;
    const password = formData.get('password') as string;

    const { error } = await signIn(email, password);

    if (error) {
      toast({
        title: "Sign In Failed",
        description: error.message,
        variant: "destructive",
      });
    } else {
      navigate('/');
    }

    setLoading(false);
  };

  const handleGoogleSignIn = async () => {
    setLoading(true);

    // Different OAuth handling for web vs mobile
    const isNative = Capacitor.isNativePlatform();

    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: isNative
          ? 'nearbyconnect://auth/callback' // Custom URI scheme for mobile deep linking
          : `${window.location.origin}/auth/callback`, // Web redirect to callback route
      },
    });

    if (error) {
      toast({
        title: "Google Sign In Failed",
        description: error.message,
        variant: "destructive",
      });
    }
    setLoading(false);
  };

  // Redirect if already authenticated
  if (user) {
    navigate('/');
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/10 to-secondary/10 flex items-center justify-center p-4 mobile-safe-area">
      <div className="w-full max-w-md space-y-6">
        {/* App Header */}
        <div className="text-center space-y-2">
          <div className="flex justify-center">
            <div className="relative">
              <Logo size={80} />
            </div>
          </div>
          <h1 className="mt-2 text-2xl font-bold">NearbyConnect</h1>
          <p className="text-muted-foreground">Connect with people around you</p>
        </div>

        {/* Features Preview */}
        <div className="grid grid-cols-3 gap-4 text-center">
          <div className="space-y-2">
            <MapPin className="h-6 w-6 mx-auto text-primary" />
            <p className="text-xs text-muted-foreground">Location Based</p>
          </div>
          <div className="space-y-2">
            <Users className="h-6 w-6 mx-auto text-primary" />
            <p className="text-xs text-muted-foreground">Find Friends</p>
          </div>
          <div className="space-y-2">
            <MessageCircle className="h-6 w-6 mx-auto text-primary" />
            <p className="text-xs text-muted-foreground">Chat Safely</p>
          </div>
        </div>

        {/* Auth Tabs */}
        <Card>
          <Tabs defaultValue="signin" className="w-full">
            <CardHeader>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="signin">Sign In</TabsTrigger>
                <TabsTrigger value="signup">Sign Up</TabsTrigger>
              </TabsList>
            </CardHeader>

            <TabsContent value="signin">
              <form onSubmit={handleSignIn}>
                <CardContent className="space-y-4">
                  <CardTitle>Welcome Back</CardTitle>
                  <CardDescription>
                    Sign in to continue connecting with people nearby
                  </CardDescription>
                  <div className="space-y-2">
                    <Label htmlFor="signin-email">Email</Label>
                    <Input
                      id="signin-email"
                      name="email"
                      type="email"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="signin-password">Password</Label>
                    <Input
                      id="signin-password"
                      name="password"
                      type="password"
                      required
                    />
                  </div>
                  <Button
                    type="submit"
                    className="w-full"
                    disabled={loading}
                  >
                    {loading ? "Signing In..." : "Sign In"}
                  </Button>
                </CardContent>
                <CardFooter>
                  <Button
                    type="button"
                    className="w-full mt-2 flex items-center justify-center gap-2"
                    onClick={handleGoogleSignIn}
                    disabled={loading}
                  >
                    <img src="https://www.svgrepo.com/show/475656/google-color.svg" alt="Google" className="h-5 w-5" />
                    {loading ? "Signing In..." : "Sign in with Google"}
                  </Button>
                </CardFooter>
              </form>
            </TabsContent>

            <TabsContent value="signup">
              <form onSubmit={handleSignUp}>
                <CardContent className="space-y-4">
                  <CardTitle>Join Nearby Connect</CardTitle>
                  <CardDescription>
                    Start connecting instantly with people around you
                  </CardDescription>
                  <div className="space-y-2">
                    <Label htmlFor="signup-fullname">Full Name</Label>
                    <Input
                      id="signup-fullname"
                      name="fullName"
                      type="text"
                      placeholder="John Doe"
                      value={signupForm.fullName}
                      onChange={handleSignupInput}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="signup-username">Username</Label>
                    <Input
                      id="signup-username"
                      name="username"
                      type="text"
                      placeholder="johndoe"
                      value={signupForm.username.trim().toLowerCase()}
                      onChange={handleSignupInput}
                      required
                      pattern="^[a-zA-Z0-9_]{3,}$"
                      title="Username must be at least 3 characters long and can only contain letters, numbers, and underscores."
                      minLength={3}
                      className={signupErrors.usernameUnique ? '' : 'border-red-500'}
                    />
                    {!signupErrors.usernameUnique && (
                      <p className="text-xs text-red-500 mt-1">Username already taken.</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="signup-email">Email</Label>
                    <Input
                      id="signup-email"
                      name="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={signupForm.email}
                      onChange={handleSignupInput}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="signup-password">Password</Label>
                    <div className="relative">
                      <Input
                        id="signup-password"
                        name="password"
                        type="password"
                        placeholder="Create a strong password"
                        value={signupForm.password}
                        onChange={handleSignupInput}
                        required
                        minLength={8}
                        pattern="^.{8,}$"
                        title="Password must be at least 8 characters."
                      />
                      <span className="absolute right-2 top-2 text-muted-foreground">
                        <svg width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><rect width="18" height="11" x="3" y="11" rx="2" /><path d="M7 11V7a5 5 0 0 1 10 0v4" /></svg>
                      </span>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">Password must be at least 8 characters.</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="signup-confirm-password">Confirm Password</Label>
                    <Input
                      id="signup-confirm-password"
                      name="confirmPassword"
                      type="password"
                      placeholder="Re-enter your password"
                      value={signupForm.confirmPassword}
                      onChange={handleSignupInput}
                      required
                      className={signupErrors.passwordMatch ? '' : 'border-red-500'}
                    />
                    {!signupErrors.passwordMatch && (
                      <p className="text-xs text-red-500 mt-1">Passwords do not match.</p>
                    )}
                  </div>
                  <Button
                    type="button"
                    className="w-full mt-2 flex items-center justify-center gap-2"
                    onClick={handleGoogleSignIn}
                    disabled={loading}
                  >
                    <img src="https://www.svgrepo.com/show/475656/google-color.svg" alt="Google" className="h-5 w-5" />
                    {loading ? "Signing Up..." : "Sign up with Google"}
                  </Button>
                </CardContent>
                <CardFooter>
                  <Button
                    type="submit"
                    className="w-full flex items-center justify-center gap-2"
                    disabled={loading}
                  >
                    {loading && (
                      <span className="animate-spin rounded-full h-4 w-4 border-2 border-t-2 border-muted border-t-primary" />
                    )}
                    {loading ? "Creating Account..." : "Create Account"}
                  </Button>
                </CardFooter>
              </form>
            </TabsContent>
          </Tabs>
        </Card>
      </div>
    </div>
  );
};

export default Auth;