import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { logger, logAuthEvent } from '@/utils/logger';

const OAuthCallback = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const handleOAuthCallback = async () => {
      try {
        logAuthEvent('OAuth callback triggered');

        // Get the current URL
        const url = window.location.href;
        logger.log('Web OAuth callback URL:', url);

        // Check for error parameters first
        const urlParams = new URLSearchParams(window.location.search);
        const error = urlParams.get('error');
        const errorDescription = urlParams.get('error_description');

        if (error) {
          logAuthEvent('OAuth error', undefined, { error, errorDescription });
          toast({
            title: "Sign In Failed",
            description: errorDescription || error,
            variant: "destructive",
          });
          navigate('/auth');
          return;
        }
        
        // Handle the OAuth session
        const { data, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) {
          logAuthEvent('Session error', undefined, { error: sessionError.message });
          toast({
            title: "Sign In Failed",
            description: sessionError.message,
            variant: "destructive",
          });
          navigate('/auth');
          return;
        }

        if (data.session) {
          logAuthEvent('OAuth session found', data.session.user?.id);
          toast({
            title: "Welcome!",
            description: "Successfully signed in with Google.",
          });
          navigate('/');
        } else {
          logAuthEvent('No session found, redirecting to auth');
          navigate('/auth');
        }
      } catch (err) {
        logAuthEvent('OAuth callback error', undefined, { error: err });
        toast({
          title: "Sign In Failed",
          description: "An unexpected error occurred.",
          variant: "destructive",
        });
        navigate('/auth');
      }
    };

    handleOAuthCallback();
  }, [navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-t-2 border-muted border-t-primary mx-auto mb-4" />
        <p className="text-muted-foreground">Completing sign in...</p>
      </div>
    </div>
  );
};

export default OAuthCallback;
