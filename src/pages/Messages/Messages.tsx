import './Messages.css';
import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MessageCircle, ArrowLeft } from 'lucide-react';
import MobileNavigation from '@/components/MobileNavigation';
import ChatView from './ChatView';
import { supabase } from '@/integrations/supabase/client';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { logger } from '@/utils/logger';

interface ConnectionRow { requester_id: string; receiver_id: string; }
interface PartnerProfile { user_id: string; full_name: string; username: string; avatar_url: string | null; is_business: boolean; }

const Messages = () => {
  const navigate = useNavigate();
  const [params] = useSearchParams();
  const currentConvId = params.get('c');
  const { user, loading } = useAuth();
  const [partners, setPartners] = useState<PartnerProfile[]>([]);
  const [loadingPartners, setLoadingPartners] = useState(false);

  useEffect(() => {
    if (!loading && !user) navigate('/auth');
  }, [user, loading, navigate]);

  useEffect(() => {
    if (!user) return;
    setLoadingPartners(true);
    (async () => {
      try {
        const uid = user.id;
        const sb = supabase;
        const { data: cons, error: consErr } = await sb
          .from('connections')
          .select('requester_id, receiver_id')
          .eq('status', 'accepted')
          .or(`requester_id.eq.${uid},receiver_id.eq.${uid}`);
        if (consErr) throw consErr;
        const ids = Array.from(new Set((cons as ConnectionRow[]).map(r => r.requester_id === uid ? r.receiver_id : r.requester_id)));
        if (ids.length === 0) { setPartners([]); setLoadingPartners(false); return; }
        const { data: profs, error: profErr } = await sb
          .from('profiles')
          .select('user_id, full_name, username, avatar_url, is_business')
          .in('user_id', ids);
        if (profErr) throw profErr;
        setPartners((profs || []) as PartnerProfile[]);
      } catch (e) {
        logger.error('Failed to load connections', e);
        setPartners([]);
      } finally {
        setLoadingPartners(false);
      }
    })();
  }, [user]);

  const openChat = async (partnerId: string) => {
    try {
      const { data, error } = await supabase.rpc('get_or_create_conversation', { partner_id: partnerId });
      if (error) throw error;
      navigate(`/messages?c=${data}&u=${partnerId}`);
    } catch (e) {
      console.error('Cannot open chat', e);
    }
  };

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <MessageCircle className="h-12 w-12 text-primary mx-auto pulse-location" />
          <p className="text-muted-foreground">Loading messages...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background pb-20 mobile-safe-area">
      {/* Header */}
      <header className="messages-header bg-card border-b border-border sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Button variant="ghost" size="icon" onClick={() => navigate('/')} className="md:hidden">
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <MessageCircle className="h-6 w-6 text-primary" />
              <h1 className="text-xl font-bold">Messages</h1>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content: two-pane on md+, stacked on mobile */}
      <main className="container mx-auto px-4 py-6 h-[calc(100vh-120px)]">
        <Card className="h-full">
          <CardContent className="p-0 h-full">
            <div className="h-full flex">
              {/* Left: connections list */}
              <div className="w-full md:w-80 border-r h-full overflow-y-auto">
                <div className="p-3 text-sm font-semibold text-muted-foreground">Chats  </div>
                {loadingPartners ? (
                  <div className="p-3 text-sm text-muted-foreground">Loading…</div>
                ) : partners.length === 0 ? (
                  <div className="p-3 text-sm text-muted-foreground">No accepted connections yet.</div>
                ) : (
                  <ul className="divide-y">
                    {partners.map((p) => (
                      <li key={p.user_id}>
                        <button
                          className={`w-full flex items-center gap-3 px-3 py-3 hover:bg-accent ${currentConvId ? '' : ''}`}
                          onClick={() => openChat(p.user_id)}
                        >
                          <Avatar>
                            <AvatarImage src={p.avatar_url ?? undefined} />
                            <AvatarFallback>{(p.full_name || p.username || '?').charAt(0)}</AvatarFallback>
                          </Avatar>
                          <div className="flex flex-col items-start">
                            <div className="font-medium text-sm">{p.full_name || p.username}</div>
                            {p.is_business && <Badge variant="outline" className="text-xs">Business</Badge>}
                            <div className="text-xs text-muted-foreground">Tap to chat</div>
                          </div>
                        </button>
                      </li>
                    ))}
                  </ul>
                )}
              </div>

              {/* Right: chat view */}
              <div className="hidden md:block flex-1 h-full">
                <ChatView />
              </div>
              {/* On mobile, show chat view below list when c param is present */}
            </div>
            {/* Mobile chat view */}
            <div className="md:hidden mt-2">
              {currentConvId ? (
                <ChatView />
              ) : (
                <div className="p-4 text-sm text-muted-foreground">Select a connection to start chatting.</div>
              )}
            </div>
          </CardContent>
        </Card>
      </main>

      <MobileNavigation />
    </div>
  );
};

export default Messages;