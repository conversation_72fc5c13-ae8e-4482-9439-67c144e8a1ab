import { useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { logger, devOnly } from '@/utils/logger';

interface Profile {
  id: string;
  username?: string;
  full_name?: string;
  avatar_url?: string;
  bio?: string;
  interests?: string[];
  is_visible?: boolean;
  is_business?: boolean;
}

interface MessageRow {
  id: string;
  sender_id: string;
  receiver_id: string;
  content: string;
  created_at: string;
  is_read: boolean | null;
}

import { useAuth } from '@/hooks/useAuth';

const ChatView = () => {
  const [params] = useSearchParams();
  const conversationId = params.get('c');
  const partnerIdParam = params.get('u');
  const [messages, setMessages] = useState<MessageRow[]>([]);
  const [input, setInput] = useState('');
  const [sending, setSending] = useState(false);
  const [receiverId, setReceiverId] = useState<string>('');
  const [receiverName, setReceiverName] = useState<string>('');
  const [receiverProfile, setReceiverProfile] = useState<Profile | null>(null);
  const [showProfile, setShowProfile] = useState(false);
  const [warning, setWarning] = useState('');
  const [realtimeStatus, setRealtimeStatus] = useState<string>('CONNECTING');
  const [isRealtimeConnected, setIsRealtimeConnected] = useState(false);
  const [hasMoreMessages, setHasMoreMessages] = useState(true);
  const [loadingOlderMessages, setLoadingOlderMessages] = useState(false);
  const [initialLoad, setInitialLoad] = useState(true);
  const listRef = useRef<HTMLDivElement | null>(null);
  const { user } = useAuth();

  // Initialize receiver from URL param if provided
  useEffect(() => {
    if (partnerIdParam) {
      setReceiverId(partnerIdParam);
    }
  }, [partnerIdParam]);

  // Fetch receiver profile when we have receiverId
  useEffect(() => {
    if (!receiverId) return;
    (async () => {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('id, full_name, username, avatar_url, bio, interests, is_business')
        .eq('user_id', receiverId)
        .single();
      if (error) {
        logger.error('Error fetching receiver profile:', error);
        setReceiverName('Unknown');
        setReceiverProfile(null);
      } else {
        setReceiverName(profile.full_name || 'Unknown');
        setReceiverProfile(profile);
      }
    })();
  }, [receiverId]);

  const scrollToBottom = () => {
    listRef.current?.scrollTo({ top: listRef.current.scrollHeight, behavior: 'smooth' });
  };

  // WhatsApp-like pagination: Load older messages
  const loadOlderMessages = async () => {
    if (!conversationId || loadingOlderMessages || !hasMoreMessages) return;

    setLoadingOlderMessages(true);
    const oldestMessage = messages[0];
    const beforeTimestamp = oldestMessage?.created_at;

    // Save current scroll position to maintain it after loading
    const scrollContainer = listRef.current;
    const previousScrollHeight = scrollContainer?.scrollHeight || 0;

    try {
      const { data, error } = await supabase
        .from('messages')
        .select('id,sender_id,receiver_id,content,created_at,is_read')
        .eq('conversation_id', conversationId)
        .lt('created_at', beforeTimestamp)
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) {
        logger.error('Failed to load older messages:', error);
        return;
      }

      if (data && data.length > 0) {
        // Reverse to maintain chronological order and prepend to existing messages
        const olderMessages = data.reverse();
        setMessages(prev => [...olderMessages, ...prev]);

        // Maintain scroll position after loading older messages
        setTimeout(() => {
          if (scrollContainer) {
            const newScrollHeight = scrollContainer.scrollHeight;
            const scrollDifference = newScrollHeight - previousScrollHeight;
            scrollContainer.scrollTop = scrollDifference;
          }
        }, 50);

        // If we got fewer than 20 messages, we've reached the beginning
        if (data.length < 20) {
          setHasMoreMessages(false);
        }

        logger.log(`Loaded ${data.length} older messages`);
      } else {
        setHasMoreMessages(false);
        logger.log('No more older messages to load');
      }
    } catch (error) {
      logger.error('Error loading older messages:', error);
    } finally {
      setLoadingOlderMessages(false);
    }
  };

  // Test function to verify real-time subscription (development only)
  const testRealtimeConnection = async () => {
    // Only run in development mode
    if (process.env.NODE_ENV !== 'development') {
      console.warn('Debug function disabled in production');
      return;
    }

    logger.group('🧪 Testing Real-time Connection');

    // Test 1: Check auth status
    const { data: session, error: sessionError } = await supabase.auth.getSession();
    logger.log('Auth session:', session?.session?.user?.id, sessionError);

    // Test 2: Check if we can read messages (RLS test)
    const { data: messages, error: messagesError } = await supabase
      .from('messages')
      .select('id')
      .eq('conversation_id', conversationId)
      .limit(1);
    logger.log('Can read messages:', messages?.length, messagesError);

    // Test 3: Check conversation participants
    const { data: participants, error: participantsError } = await supabase
      .from('conversation_participants')
      .select('user_id')
      .eq('conversation_id', conversationId);
    logger.log('Conversation participants:', participants, participantsError);

    // Test 4: Test a simple broadcast
    const testChannel = supabase.channel('test-channel');
    testChannel.on('broadcast', { event: 'test' }, (payload) => {
      logger.log('Broadcast test received:', payload);
    }).subscribe((status) => {
      logger.log('Test channel status:', status);
      if (status === 'SUBSCRIBED') {
        testChannel.send({
          type: 'broadcast',
          event: 'test',
          payload: { message: 'test broadcast' }
        });
        setTimeout(() => supabase.removeChannel(testChannel), 2000);
      }
    });

    // Test 5: Try the ping function
    try {
      const { data: pingResult, error: pingError } = await (supabase as any).rpc('ping_realtime');
      logger.log('Ping realtime result:', pingResult, pingError);
    } catch (e) {
      logger.log('Ping realtime failed:', e);
    }

    // Test 6: Try a simple WebSocket connection test
    try {
      const testChannel = supabase.channel('connection-test');
      testChannel.on('broadcast', { event: 'test' }, (payload) => {
        logger.log('✅ WebSocket connection working - received broadcast:', payload);
      }).subscribe((status) => {
        logger.log('Test channel status:', status);
        if (status === 'SUBSCRIBED') {
          logger.log('✅ WebSocket connection established successfully');
          testChannel.send({
            type: 'broadcast',
            event: 'test',
            payload: { message: 'Connection test successful' }
          });
          setTimeout(() => supabase.removeChannel(testChannel), 3000);
        } else if (status === 'CHANNEL_ERROR') {
          logger.error('❌ WebSocket connection failed');
        }
      });
    } catch (e) {
      logger.error('WebSocket test failed:', e);
    }

    logger.groupEnd();
  };

  useEffect(() => {
    if (!conversationId || !user?.id) return;

    let ignore = false;
    let channel: any = null;
    let retryCount = 0;
    const maxRetries = 3;

    const setupRealtimeSubscription = async () => {
      try {
        // Wait for authentication to be fully established
        const { data: session, error: sessionError } = await supabase.auth.getSession();
        if (sessionError || !session?.session) {
          logger.error('❌ No valid session for real-time subscription:', sessionError);
          setRealtimeStatus('CHANNEL_ERROR');
          return;
        }

        // Load initial messages (WhatsApp-like: only recent messages)
        const INITIAL_MESSAGE_LIMIT = 30;
        const result = await supabase
          .from('messages')
          .select('id,sender_id,receiver_id,content,created_at,is_read')
          .eq('conversation_id', conversationId)
          .order('created_at', { ascending: false })
          .limit(INITIAL_MESSAGE_LIMIT);

        const { data, error: messagesError } = result as { data: MessageRow[], error: any };
        if (messagesError) {
          logger.error('❌ Failed to load initial messages:', messagesError);
          setRealtimeStatus('CHANNEL_ERROR');
          return;
        }

        if (!ignore && data) {
          // Reverse to show chronological order (oldest to newest)
          const chronologicalMessages = data.reverse();
          setMessages(chronologicalMessages);

          // Check if there are more messages to load
          setHasMoreMessages(data.length === INITIAL_MESSAGE_LIMIT);
          setInitialLoad(false);

          // Derive receiver from messages if not provided
          if (!receiverId && user?.id && chronologicalMessages.length > 0) {
            const last = chronologicalMessages[chronologicalMessages.length - 1];
            const other = last.sender_id !== user.id ? last.sender_id : last.receiver_id;
            if (other && other !== user.id) setReceiverId(other);
          }

          // Auto-scroll to bottom after initial load
          setTimeout(scrollToBottom, 100);
        }

        // Enhanced debugging for real-time setup
        logger.group('🔌 Setting up real-time subscription (attempt ' + (retryCount + 1) + ')');
        logger.log('Conversation ID:', conversationId);
        logger.log('User ID:', user?.id);
        logger.log('Session valid:', !!session?.session);
        logger.log('Access token present:', !!session?.session?.access_token);

        // Use a simpler channel name
        const channelName = `conv_${conversationId.replace(/-/g, '_')}`;
        logger.log('Channel name:', channelName);

        // Set status to connecting
        setRealtimeStatus('CONNECTING');

        // Create channel with minimal configuration first
        channel = supabase
          .channel(channelName)
          .on(
            'postgres_changes',
            {
              event: 'INSERT',
              schema: 'public',
              table: 'messages',
              filter: `conversation_id=eq.${conversationId}`
            },
            (payload) => {
              logger.log('🔔 Real-time message received:', payload);

              setMessages((prev) => {
                const newMessage = payload.new as MessageRow;

                // Check if message already exists to avoid duplicates
                const messageExists = prev.some(msg => msg.id === newMessage.id);
                if (messageExists) {
                  logger.log('Message already exists, skipping duplicate');
                  return prev;
                }

                // Remove any temporary messages with similar content and timestamp
                const filteredPrev = prev.filter(msg => {
                  if (!msg.id.startsWith('temp-')) return true;

                  const isSimilar = msg.content === newMessage.content &&
                    msg.sender_id === newMessage.sender_id &&
                    Math.abs(new Date(msg.created_at).getTime() - new Date(newMessage.created_at).getTime()) < 10000;

                  if (isSimilar) {
                    logger.log('Removing temporary message, replaced by real one');
                    return false;
                  }
                  return true;
                });

                const next = [...filteredPrev, newMessage];
                logger.log('Updated messages array:', next.length, 'messages');

                // Try to derive receiver if still unknown
                if (!receiverId && user?.id) {
                  const last = next[next.length - 1];
                  const other = last.sender_id !== user.id ? last.sender_id : last.receiver_id;
                  if (other && other !== user.id) setReceiverId(other);
                }
                return next;
              });
              setTimeout(scrollToBottom, 100);
            }
          )
          .subscribe((status, err) => {
            logger.log('🔌 Subscription status:', status);
            setRealtimeStatus(status);

            if (err) {
              logger.error('🔌 Subscription error:', err);
              setIsRealtimeConnected(false);
            }

            if (status === 'SUBSCRIBED') {
              logger.log('✅ Successfully subscribed to real-time updates');
              setIsRealtimeConnected(true);
              retryCount = 0; // Reset retry count on success
            } else if (status === 'CHANNEL_ERROR') {
              logger.error('❌ Channel error occurred');
              setIsRealtimeConnected(false);
              // Retry with exponential backoff
              if (retryCount < maxRetries && !ignore) {
                retryCount++;
                const delay = Math.min(1000 * Math.pow(2, retryCount), 10000);
                logger.log(`🔄 Retrying subscription in ${delay}ms...`);
                setTimeout(() => {
                  if (!ignore) {
                    setupRealtimeSubscription();
                  }
                }, delay);
              }
            } else if (status === 'TIMED_OUT') {
              logger.error('❌ Subscription timed out');
              setIsRealtimeConnected(false);
            } else if (status === 'CLOSED') {
              logger.log('🔌 Channel closed');
              setIsRealtimeConnected(false);
            } else {
              setIsRealtimeConnected(false);
            }
          });

        logger.groupEnd();
      } catch (error) {
        logger.error('❌ Error setting up real-time subscription:', error);
        setRealtimeStatus('CHANNEL_ERROR');
      }
    };

    // Small delay to ensure auth is fully established
    const timer = setTimeout(setupRealtimeSubscription, 100);

    return () => {
      ignore = true;
      clearTimeout(timer);
      setIsRealtimeConnected(false);
      setRealtimeStatus('CLOSED');
      if (channel) {
        logger.log('🔌 Cleaning up real-time subscription');
        supabase.removeChannel(channel);
      }
    };
  }, [conversationId, receiverId, user?.id]);

  // Fallback: fetch participants to determine receiver when not provided
  useEffect(() => {
    if (!conversationId || !!receiverId || !user?.id) return;
    (async () => {
      try {
        const { data, error } = await supabase
          .from('conversation_participants')
          .select('user_id')
          .eq('conversation_id', conversationId);
        if (error) {
          logger.error('Error fetching participants:', error);
          return;
        }
        if (Array.isArray(data)) {
          const other = data.find((p: { user_id: string }) => p.user_id !== user.id);
          if (other?.user_id) setReceiverId(other.user_id);
        }
      } catch (e) {
        logger.error('Error resolving receiver from participants', e);
      }
    })();
  }, [conversationId, receiverId, user?.id]);

  useEffect(() => {
    scrollToBottom();
  }, [messages.length]);

  const send = async () => {
    if (!conversationId || !input.trim() || !user?.id) return;
    setSending(true);

    // Debug auth context and conversation details
    devOnly(() => {
      logger.group('=== DEBUGGING AUTH CONTEXT ===');
      logger.log('Frontend user.id:', user.id);
      logger.log('Frontend user object:', user);
      logger.log('Conversation ID:', conversationId);
      logger.log('Receiver ID:', receiverId);
      logger.log('Partner ID Param:', partnerIdParam);
      logger.log('Current user ID from auth context:', user.id);
      logger.groupEnd();
    });

    // Test the RLS policy conditions manually (dev only)
    devOnly(async () => {
      try {
        const { data: participantCheck, error: participantError } = await supabase
          .from('conversation_participants')
          .select('user_id')
          .eq('conversation_id', conversationId)
          .eq('user_id', user.id);
        logger.log('Participant check result:', participantCheck);
        logger.log('Participant check error:', participantError);
      } catch (e) {
        logger.log('Participant check failed:', e);
      }
    });

    const doInsert = async (convId: string, receiverUserId?: string) => {
      const finalReceiverId = receiverUserId || receiverId;

      // Build insert object - only include receiver_id if we have a valid UUID
      const insert: any = {
        conversation_id: convId,
        sender_id: user.id,
        content: input.trim(),
        is_read: false
      };

      // Only set receiver_id if we have a valid UUID (let trigger handle it otherwise)
      if (finalReceiverId && finalReceiverId.length === 36) {
        insert.receiver_id = finalReceiverId;
      }

      // Debug message insert (dev only)
      logger.log('About to insert message:', insert);

      return supabase.from('messages').insert(insert);
    };

    const ensureConversation = async (partnerId: string) => {
      const { data, error } = await supabase.rpc('get_or_create_conversation', { partner_id: partnerId });
      if (error || !data || typeof data !== 'string' || data.length !== 36) return undefined;
      return data as string;
    };

    try {
      let convId = conversationId;
      const partner = partnerIdParam || receiverId;
      let { error } = await doInsert(convId, partner);

      // Debug first insert attempt
      logger.log('First insert attempt error:', error);

      if (error) {
        // Try to self-heal by ensuring a proper conversation exists with both participants
        if (partner) {
          const newConv = await ensureConversation(partner);
          if (newConv) {
            convId = newConv;
            const usp = new URLSearchParams(window.location.search);
            usp.set('c', convId);
            if (partnerIdParam) usp.set('u', partnerIdParam);
            window.history.replaceState({}, '', `${window.location.pathname}?${usp.toString()}`);
            ({ error } = await doInsert(convId, partner));

            // Debug second insert attempt
            logger.log('Second insert attempt error:', error);
          }
        }
      }

      if (error) throw error;

      // Optimistic update: immediately add the message to the UI
      const optimisticMessage: MessageRow = {
        id: `temp-${Date.now()}`, // Temporary ID
        sender_id: user.id,
        receiver_id: partner || receiverId || '',
        content: input.trim(),
        created_at: new Date().toISOString(),
        is_read: false
      };

      setMessages(prev => {
        // Check if we already have this message (avoid duplicates)
        const hasMessage = prev.some(msg =>
          msg.content === optimisticMessage.content &&
          msg.sender_id === optimisticMessage.sender_id &&
          Math.abs(new Date(msg.created_at).getTime() - new Date(optimisticMessage.created_at).getTime()) < 5000
        );

        if (hasMessage) return prev;
        return [...prev, optimisticMessage];
      });

      setInput('');
      setTimeout(scrollToBottom, 100);
    } catch (e: unknown) {
      logger.error('Send failed', e);
      let msg = '';
      if (typeof e === 'object' && e !== null && 'message' in e && typeof (e as { message: unknown }).message === 'string') {
        msg = (e as { message: string }).message;
      }
      if (msg.includes('No accepted connection')) {
        setWarning('You can only message accepted connections.');
      } else if (msg.includes('row-level security')) {
        setWarning('Cannot send message due to permissions. Make sure you are connected.');
      } else {
        setWarning('Failed to send message.');
      }
    } finally {
      setSending(false);
    }
  };

  if (!conversationId) {
    return <div className="p-4 text-sm text-muted-foreground">No conversation selected.</div>;
  }

  // WhatsApp-style chat bubble classes
  const getBubbleClass = (msg: MessageRow) =>
    msg.sender_id === user?.id
      ? 'bg-green-500 text-white self-end rounded-br-none'
      : 'bg-white text-gray-900 self-start rounded-bl-none border';

  return (
    <div className="flex flex-col h-full bg-[#ece5dd]">
      {/* Header with receiver info */}
      <div className="flex items-center gap-3 px-4 py-3 bg-[#075e54] text-white shadow-md sticky top-0 z-10">
        <Avatar className="w-10 h-10 border-2 border-white">
          <AvatarImage src={receiverProfile?.avatar_url ?? undefined} />
          <AvatarFallback className="bg-gray-300 text-gray-800 font-bold">
            {receiverName ? receiverName[0].toUpperCase() : '?'}
          </AvatarFallback>
        </Avatar>
        <div className="flex-1">
          <div className="font-semibold text-base">{receiverName || 'Recipient'}</div>
          <div className="text-xs opacity-75 flex items-center gap-1">
            <div className={`w-2 h-2 rounded-full ${isRealtimeConnected ? 'bg-green-400' :
                realtimeStatus === 'CONNECTING' ? 'bg-yellow-400' : 'bg-red-400'
              }`}></div>
            {isRealtimeConnected ? 'Live' :
              realtimeStatus === 'CONNECTING' ? 'Connecting...' : 'Offline'}
          </div>
        </div>
        <div className="flex gap-2">
          {receiverProfile && (
            <Button variant="outline" size="sm" className="text-[#075e54] bg-white border-none hover:bg-gray-100" onClick={() => setShowProfile(true)}>
              View Profile
            </Button>
          )}
          {/* Debug button - only show in development */}
          {process.env.NODE_ENV === 'development' && (
            <Button variant="outline" size="sm" className="text-[#075e54] bg-white border-none hover:bg-gray-100" onClick={testRealtimeConnection}>
              🧪 Test RT
            </Button>
          )}
        </div>
      </div>

      {/* Warning for missing recipient */}
      {warning && (
        <div className="bg-yellow-100 text-yellow-800 px-4 py-2 text-sm text-center">{warning}</div>
      )}

      {/* Chat messages */}
      <div ref={listRef} className="flex-1 overflow-y-auto px-2 py-4 flex flex-col gap-2">
        {/* Load Older Messages Button */}
        {hasMoreMessages && messages.length > 0 && (
          <div className="flex justify-center mb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={loadOlderMessages}
              disabled={loadingOlderMessages}
              className="text-[#075e54] bg-white border-gray-300 hover:bg-gray-50"
            >
              {loadingOlderMessages ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#075e54] mr-2"></div>
                  Loading...
                </>
              ) : (
                'Load older messages'
              )}
            </Button>
          </div>
        )}

        {messages.map((m) => (
          <div
            key={m.id}
            className={`max-w-[70%] px-4 py-2 rounded-2xl shadow-sm ${getBubbleClass(m)}`}
            style={{ alignSelf: m.sender_id === user?.id ? 'flex-end' : 'flex-start' }}
          >
            <div className="text-sm">{m.content}</div>
            <div className="text-xs opacity-60 mt-1 text-right">
              {new Date(m.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </div>
          </div>
        ))}

        {/* Show loading indicator for initial load */}
        {initialLoad && messages.length === 0 && (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#075e54]"></div>
            <span className="ml-2 text-gray-500">Loading messages...</span>
          </div>
        )}

        {/* Show empty state if no messages */}
        {!initialLoad && messages.length === 0 && (
          <div className="flex justify-center items-center py-8 text-gray-500">
            <div className="text-center">
              <div className="text-lg mb-2">💬</div>
              <div>Start your conversation!</div>
              <div className="text-sm">Send a message to begin chatting</div>
            </div>
          </div>
        )}
      </div>

      {/* Message input */}
      <div className="p-3 border-t flex gap-2 bg-[#f7f7f7]">
        <Input
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="Type a message"
          onKeyDown={(e) => e.key === 'Enter' && send()}
          className="flex-1 rounded-full px-4 py-2 border border-gray-300 bg-white focus:outline-none focus:ring-2 focus:ring-[#075e54]"
        />
        <Button
          onClick={send}
          disabled={sending || !input.trim()}
          className="rounded-full px-6 bg-[#25d366] text-white font-bold hover:bg-[#20b358] disabled:bg-gray-300"
        >
          Send
        </Button>
      </div>

      {/* Receiver profile modal */}
      {showProfile && receiverProfile && (
        <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-xs relative">
            <button
              className="absolute top-2 right-2 text-gray-500 hover:text-gray-800"
              onClick={() => setShowProfile(false)}
            >
              ×
            </button>
            <div className="flex flex-col items-center gap-2">
              <Avatar className="w-20 h-20">
                <AvatarImage src={receiverProfile.avatar_url ?? undefined} />
                <AvatarFallback className="bg-gray-300 text-gray-800 font-bold text-3xl">
                  {receiverName ? receiverName[0].toUpperCase() : '?'}
                </AvatarFallback>
              </Avatar>
              <div className="font-semibold text-lg mt-2">{receiverName}</div>
              {receiverProfile.username && <div className="text-gray-600 text-sm text-center">@{receiverProfile.username}</div>}
              {receiverProfile.bio && <div className="text-gray-600 text-sm text-center">{receiverProfile.bio}</div>}

              {receiverProfile.interests && receiverProfile.interests.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {receiverProfile.interests.map((interest, idx) => (
                    <Badge key={idx} variant="outline" className="text-xs">
                      {interest}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatView;
