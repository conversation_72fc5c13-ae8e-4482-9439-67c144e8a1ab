import './Connections.css';
import { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Users, UserPlus, ArrowLeft, Search, MapPin, Clock } from 'lucide-react';
import MobileNavigation from '@/components/MobileNavigation';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { logger, logDbOperation, logAuthEvent } from '@/utils/logger';

interface Profile {
  id: string;
  user_id: string;
  full_name: string;
  username: string;
  bio: string | null;
  interests: string[] | null;
  is_business: boolean | null;
  distance_m: number | null;
  connection_status?: 'none' | 'pending' | 'accepted' | 'rejected';
}

interface Connection {
  id: string;
  status: string;
  created_at: string;
  requester: Profile;
  receiver: Profile;
}

const Connections = () => {
  const navigate = useNavigate();
  const { user, loading } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Profile[]>([]);
  const [nearbyPeople, setNearbyPeople] = useState<Profile[]>([]);

  // Helper to get connection status for a profile
  const getConnectionStatus = (profile: Profile): 'none' | 'pending' | 'accepted' | 'rejected' => {
    if (!user) return 'none';
    // Check if already connected
    if (connections.some(c =>
      (c.requester.user_id === user.id && c.receiver.user_id === profile.user_id) ||
      (c.receiver.user_id === user.id && c.requester.user_id === profile.user_id)
    )) {
      return 'accepted';
    }
    // Check if there is any pending request between the users (either direction)
    if (requests.some(r =>
      ((r.requester.user_id === user.id && r.receiver.user_id === profile.user_id) ||
        (r.requester.user_id === profile.user_id && r.receiver.user_id === user.id)) &&
      r.status === 'pending'
    )) {
      return 'pending';
    }
    // Check if there is any rejected request between the users (either direction)
    if (requests.some(r =>
      ((r.requester.user_id === user.id && r.receiver.user_id === profile.user_id) ||
        (r.requester.user_id === profile.user_id && r.receiver.user_id === user.id)) &&
      r.status === 'rejected'
    )) {
      return 'rejected';
    }
    return 'none';
  };
  const [connections, setConnections] = useState<Connection[]>([]);
  const [requests, setRequests] = useState<Connection[]>([]);
  const [loadingSearch, setLoadingSearch] = useState(false);
  const [loadingNearby, setLoadingNearby] = useState(false);

  useEffect(() => {
    if (!loading && !user) {
      navigate('/auth');
    }
  }, [user, loading, navigate]);

  const fetchNearbyPeople = useCallback(async () => {
    setLoadingNearby(true);
    try {
      // First, get current user's location
      const { data: userProfile, error: profileError } = await supabase
        .from('profiles')
        .select('location_lat, location_lng')
        .eq('user_id', user?.id)
        .single();

      if (profileError || !userProfile?.location_lat || !userProfile?.location_lng) {
        // If user doesn't have location, show all people
        const { data, error } = await supabase
          .from('profiles')
          .select('id, user_id, full_name, username, bio, interests, is_business')
          .neq('user_id', user?.id)
          .eq('is_visible', true)
          .limit(20);

        if (error) {
          console.error('Error fetching people:', error);
        } else {
          // Add distance_m property since it's required by Profile interface
          const peopleWithDistance = (data || []).map(person => ({
            ...person,
            distance_m: null // No distance available when location is not set
          }));
          setNearbyPeople(peopleWithDistance);
        }
        return;
      }

      // Use the location-based search function
      const { data, error } = await supabase
        .rpc('find_nearby_users', {
          user_lat: userProfile.location_lat,
          user_lng: userProfile.location_lng,
          radius_m: 1000000 // 1000000m radius (1000km for wide search)
        });

      if (error) {
        console.error('Error fetching nearby people:', error);
        toast({
          title: "Error",
          description: "Failed to find people nearby",
          variant: "destructive",
        });
      } else {
        setNearbyPeople(data || []);
      }
    } catch (error) {
      console.error('Error fetching people:', error);
    } finally {
      setLoadingNearby(false);
    }
  }, [user]);

  const fetchConnections = useCallback(async () => {
    try {
      const { data: connectionsData, error } = await supabase
        .from('connections')
        .select('id, status, created_at, requester_id, receiver_id')
        .eq('status', 'accepted')
        .or(`requester_id.eq.${user?.id},receiver_id.eq.${user?.id}`);

      if (error) {
        logger.error('Error fetching connections:', error);
        setConnections([]);
        return;
      }

      if (!connectionsData || connectionsData.length === 0) {
        setConnections([]);
        return;
      }

      // Collect all unique user_ids (excluding self)
      const userIds = Array.from(new Set(
        connectionsData
          .map(c => [c.requester_id, c.receiver_id])
          .flat()
          .filter(uid => uid && uid !== user?.id)
      ));

      // Fetch all profiles in one query
      const { data: profiles, error: profileError } = await supabase
        .from('profiles')
        .select('id, user_id, full_name, username, bio, interests, is_business')
        .in('user_id', userIds);

      if (profileError) {
        console.error('Error fetching connection profiles:', profileError);
        setConnections([]);
        return;
      }

      // Map user_id to profile
      const profileMap = new Map();
      profiles?.forEach((p) => profileMap.set(p.user_id, p));

      // Build connections with profile info
      const connectionsWithProfiles = connectionsData.map((conn) => ({
        id: conn.id,
        status: conn.status,
        created_at: conn.created_at,
        requester: profileMap.get(conn.requester_id) || { user_id: conn.requester_id, full_name: 'Unknown', username: '', id: '', bio: null, interests: null, is_business: null },
        receiver: profileMap.get(conn.receiver_id) || { user_id: conn.receiver_id, full_name: 'Unknown', username: '', id: '', bio: null, interests: null, is_business: null },
      }));

      // Deduplicate so each unique friend appears only once
      const seen = new Set();
      const dedupedConnections = [];
      for (const conn of connectionsWithProfiles) {
        // The friend is the other user in the connection
        const friendId = conn.requester.user_id === user.id ? conn.receiver.user_id : conn.requester.user_id;
        if (!seen.has(friendId)) {
          seen.add(friendId);
          dedupedConnections.push(conn);
        }
      }

      setConnections(dedupedConnections);
    } catch (error) {
      console.error('Error fetching connections:', error);
      setConnections([]);
    }
  }, [user]);

  const fetchRequests = useCallback(async () => {
    try {
      // Fetch both incoming and outgoing pending requests
      const { data: requestsData, error } = await supabase
        .from('connections')
        .select('id, status, created_at, requester_id, receiver_id')
        .eq('status', 'pending')
        .or(`requester_id.eq.${user?.id},receiver_id.eq.${user?.id}`);

      if (error) {
        console.error('Error fetching requests:', error);
        return;
      }

      if (!requestsData || requestsData.length === 0) {
        setRequests([]);
        return;
      }

      // Collect all unique user_ids (excluding self) to fetch their profiles
      const userIds = Array.from(new Set(
        requestsData
          .map(req => [req.requester_id, req.receiver_id])
          .flat()
          .filter(uid => uid && uid !== user?.id)
      ));

      if (userIds.length === 0) {
        setRequests([]);
        return;
      }

      // Fetch profiles for all users involved in requests
      const { data: profiles, error: profileError } = await supabase
        .from('profiles')
        .select('id, user_id, full_name, username, bio, interests, is_business')
        .in('user_id', userIds);

      if (profileError) {
        console.error('Error fetching profiles:', profileError);
        return;
      }

      // Combine data - create proper requester/receiver objects
      const requestsWithProfiles = requestsData?.map(request => {
        const requesterProfile = profiles?.find(p => p.user_id === request.requester_id);
        const receiverProfile = profiles?.find(p => p.user_id === request.receiver_id);

        return {
          ...request,
          requester: requesterProfile ? { ...requesterProfile, distance_m: null } : {
            id: '',
            user_id: request.requester_id,
            full_name: request.requester_id === user?.id ? 'You' : 'Unknown User',
            username: request.requester_id === user?.id ? 'you' : 'unknown',
            bio: null,
            interests: null,
            is_business: false,
            distance_m: null
          },
          receiver: receiverProfile ? { ...receiverProfile, distance_m: null } : {
            id: '',
            user_id: request.receiver_id,
            full_name: request.receiver_id === user?.id ? 'You' : 'Unknown User',
            username: request.receiver_id === user?.id ? 'you' : 'unknown',
            bio: null,
            interests: null,
            is_business: false,
            distance_m: null
          }
        };
      }) || [];

      setRequests(requestsWithProfiles);
    } catch (error) {
      console.error('Error fetching requests:', error);
    }
  }, [user]);

  const searchUsers = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setLoadingSearch(true);
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, user_id, full_name, username, bio, interests, is_business')
        .neq('user_id', user?.id)
        .or(`full_name.ilike.%${query}%,username.ilike.%${query}%`)
        .limit(10);

      if (error) {
        console.error('Error searching users:', error);
      } else {
        // Add distance_m property since it's required by Profile interface
        const resultsWithDistance = (data || []).map(person => ({
          ...person,
          distance_m: null // No distance available in search results
        }));
        setSearchResults(resultsWithDistance);
      }
    } catch (error) {
      console.error('Error searching users:', error);
    } finally {
      setLoadingSearch(false);
    }
  };

  const sendConnectionRequest = async (receiverId: string) => {
    try {
      const { error } = await supabase
        .from('connections')
        .insert({
          requester_id: user?.id,
          receiver_id: receiverId,
          status: 'pending'
        });

      if (error) {
        logDbOperation('INSERT', 'connections', { error: error.message });
        toast({
          title: "Error",
          description: "Failed to send connection request",
          variant: "destructive",
        });
      } else {
        logDbOperation('INSERT', 'connections', { status: 'success', receiver_id: receiverId });
        toast({
          title: "Request Sent",
          description: "Connection request sent successfully",
        });
        // Remove from search results
        setSearchResults(prev => prev.filter(p => p.user_id !== receiverId));
      }
    } catch (error) {
      logger.error('Error sending connection request:', error);
    }
  };

  const handleConnectionRequest = async (connectionId: string, action: 'accept' | 'reject') => {
    try {
      const { data, error } = await supabase
        .from('connections')
        .update({ status: action === 'accept' ? 'accepted' : 'rejected' })
        .eq('id', connectionId)
        .select();

      if (error) {
        console.error('Error handling connection request:', error);
        toast({
          title: "Error",
          description: "Failed to handle connection request",
          variant: "destructive",
        });
      } else {
        toast({
          title: action === 'accept' ? "Request Accepted" : "Request Rejected",
          description: `Connection request ${action}ed successfully`,
        });
        fetchConnections();
        fetchRequests();

        // If accepted, create conversation and send 'hi' message
        if (action === 'accept' && data && data[0]) {
          const connection = data[0];
          // The user who accepted is the current user (user.id)
          // The other user is the one who sent the request
          const currentUserId = user?.id;
          const otherUserId = connection.requester_id === currentUserId ? connection.receiver_id : connection.requester_id;
          try {
            // Use Supabase function to get or create conversation
            const { data: convData, error: convError } = await supabase
              .rpc('get_or_create_conversation', { partner_id: otherUserId });
            if (convError || !convData || typeof convData !== 'string' || convData.length !== 36) {
              logger.error('Error creating conversation or invalid conversationId:', convError, convData);
              return;
            }
            const conversationId = convData;
            // Validate all UUIDs
            if (!conversationId || !currentUserId || !otherUserId || conversationId.length !== 36 || currentUserId.length !== 36 || otherUserId.length !== 36) {
              logger.error('Invalid UUID(s) for message insert:', { conversationId, currentUserId, otherUserId });
              return;
            }
            // Send initial message
            const { error: msgError } = await supabase
              .from('messages')
              .insert({
                conversation_id: conversationId,
                sender_id: currentUserId, // The user who accepted
                receiver_id: otherUserId, // Both UUIDs are validated above
                content: 'hi',
                is_read: false,
              });
            if (msgError) {
              logDbOperation('INSERT', 'messages', { error: msgError.message });
            }
          } catch (e) {
            logger.error('Error auto-initiating conversation:', e);
          }
        }
      }
    } catch (error) {
      console.error('Error handling connection request:', error);
    }
  };

  // Place this useEffect after fetchNearbyPeople, fetchConnections, fetchRequests are defined
  useEffect(() => {
    if (user) {
      fetchConnections();
      fetchRequests();
      fetchNearbyPeople();
    }
  }, [user, fetchConnections, fetchRequests, fetchNearbyPeople]);

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <Users className="h-12 w-12 text-primary mx-auto pulse-location" />
          <p className="text-muted-foreground">Loading connections...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background pb-20 mobile-safe-area">
      {/* Header */}
      <header className="bg-card border-b border-border sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Button 
                variant="ghost" 
                size="icon"
                onClick={() => navigate('/')}
                className="md:hidden"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <Users className="h-6 w-6 text-primary" />
              <h1 className="text-xl font-bold">Connections</h1>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                fetchConnections();
                fetchRequests();
                fetchNearbyPeople();
              }}
              className="ml-2"
            >
              Refresh
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="connections-main container mx-auto px-4 py-6">
        <Tabs defaultValue="connections" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="connections">Connections ({connections.length})</TabsTrigger>
            <TabsTrigger value="requests">Requests ({requests.length})</TabsTrigger>
            <TabsTrigger value="search">Find People</TabsTrigger>
          </TabsList>

          <TabsContent value="connections" className="space-y-4">
            {connections.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No connections yet</h3>
                  <p className="text-muted-foreground mb-4">
                    Start connecting with people around you!
                  </p>
                  <Button onClick={() => {
                    const tabsTrigger = document.querySelector('[value="search"]') as HTMLButtonElement;
                    tabsTrigger?.click();
                  }}>
                    <UserPlus className="h-4 w-4 mr-2" />
                    Find People
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-3">
                {connections.map((connection) => {
                  const friend = connection.requester.user_id === user.id 
                    ? connection.receiver 
                    : connection.requester;
                  
                  return (
                    <Card key={connection.id}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                              <span className="text-lg font-semibold text-primary">
                                {friend.full_name.charAt(0)}
                              </span>
                            </div>
                            <div>
                              <p className="font-medium">{friend.full_name}</p>
                              <p className="text-sm text-muted-foreground">@{friend.username}</p>
                              {friend.bio && (
                                <p className="text-xs text-muted-foreground mt-1">{friend.bio}</p>
                              )}
                              {friend.interests && friend.interests.length > 0 && (
                                <div className="flex flex-wrap gap-1 mt-2">
                                  {friend.interests.slice(0, 3).map((interest, idx) => (
                                    <Badge key={idx} variant="secondary" className="text-xs">
                                      {interest}
                                    </Badge>
                                  ))}
                                </div>
                              )}
                            </div>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={async () => {
                              try {
                                const partnerId = friend.user_id;
                                const { data, error } = await supabase.rpc('get_or_create_conversation', { partner_id: partnerId });
                                if (error) throw error;
                                navigate(`/messages?c=${data}&u=${partnerId}`);
                              } catch (e) {
                                toast({ title: 'Cannot message', description: 'You can only message accepted connections.', variant: 'destructive' });
                              }
                            }}
                          >
                            Message
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </TabsContent>

          <TabsContent value="requests" className="space-y-6">
            {/* Incoming Requests */}
            <div>
              <h3 className="text-lg font-semibold mb-2">Incoming requests</h3>
              {requests.filter(r => r.receiver.user_id === user.id).length === 0 ? (
                <Card>
                  <CardContent className="p-8 text-center">
                    <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No incoming requests</h3>
                    <p className="text-muted-foreground">
                      You'll see connection requests from other users here.
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-3">
                  {requests.filter(r => r.receiver.user_id === user.id).map((request) => (
                    <Card key={request.id}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                              <span className="text-lg font-semibold text-primary">
                                {request.requester.full_name.charAt(0)}
                              </span>
                            </div>
                            <div>
                              <p className="font-medium">{request.requester.full_name}</p>
                              <p className="text-sm text-muted-foreground">@{request.requester.username}</p>
                              {request.requester.bio && (
                                <p className="text-xs text-muted-foreground mt-1">{request.requester.bio}</p>
                              )}
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handleConnectionRequest(request.id, 'reject')}
                            >
                              Decline
                            </Button>
                            <Button 
                              size="sm"
                              onClick={() => handleConnectionRequest(request.id, 'accept')}
                            >
                              Accept
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
            {/* Sent Requests */}
            <div>
              <h3 className="text-lg font-semibold mb-2 mt-6">Sent requests</h3>
              {requests.filter(r => r.requester.user_id === user.id).length === 0 ? (
                <Card>
                  <CardContent className="p-8 text-center">
                    <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No sent requests</h3>
                    <p className="text-muted-foreground">
                      You haven't sent any connection requests yet.
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-3">
                  {requests.filter(r => r.requester.user_id === user.id).map((request) => (
                    <Card key={request.id}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                              <span className="text-lg font-semibold text-primary">
                                {request.receiver.full_name.charAt(0)}
                              </span>
                            </div>
                            <div>
                              <p className="font-medium">{request.receiver.full_name}</p>
                              <p className="text-sm text-muted-foreground">@{request.receiver.username}</p>
                              {request.receiver.bio && (
                                <p className="text-xs text-muted-foreground mt-1">{request.receiver.bio}</p>
                              )}
                            </div>
                          </div>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={async () => {
                              try {
                                const { error } = await supabase
                                  .from('connections')
                                  .delete()
                                  .eq('id', request.id);
                                if (error) {
                                  toast({ title: 'Error', description: 'Failed to unsend request', variant: 'destructive' });
                                } else {
                                  toast({ title: 'Request unsent', description: 'Connection request unsent successfully' });
                                  fetchRequests();
                                }
                              } catch (e) {
                                toast({ title: 'Error', description: 'Failed to unsend request', variant: 'destructive' });
                              }
                            }}
                          >
                            Unsend
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="search" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Search className="h-5 w-5" />
                  <span>Find People</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search by name or username..."
                    value={searchQuery}
                    onChange={(e) => {
                      setSearchQuery(e.target.value);
                      searchUsers(e.target.value);
                    }}
                    className="pl-10"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Show search results if there's a query */}
            {searchQuery ? (
              loadingSearch ? (
                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="animate-pulse">Searching...</div>
                  </CardContent>
                </Card>
              ) : searchResults.length > 0 ? (
                <div className="space-y-3">
                  {searchResults.map((profile) => {
                    const status = getConnectionStatus(profile);
                    return (
                      <Card key={profile.id}>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                                <span className="text-lg font-semibold text-primary">
                                  {profile.full_name.charAt(0)}
                                </span>
                              </div>
                              <div>
                                <p className="font-medium">{profile.full_name}</p>
                                <p className="text-sm text-muted-foreground">@{profile.username}</p>
                                {profile.bio && (
                                  <p className="text-xs text-muted-foreground mt-1">{profile.bio}</p>
                                )}
                                {profile.is_business && (
                                  <Badge variant="outline" className="text-xs mt-1">
                                    Business
                                  </Badge>
                                )}
                              </div>
                            </div>
                            {status === 'none' || status === 'rejected' ? (
                              <Button size="sm" onClick={() => sendConnectionRequest(profile.user_id)}>
                                <UserPlus className="h-4 w-4 mr-2" />
                              </Button>
                            ) : status === 'pending' ? (
                              <Badge variant="secondary" className="text-xs">Pending</Badge>
                            ) : status === 'accepted' ? (
                              <Button size="sm" variant="outline" onClick={async () => {
                                try {
                                  const partnerId = profile.user_id;
                                  const { data, error } = await supabase.rpc('get_or_create_conversation', { partner_id: partnerId });
                                  if (error) throw error;
                                  navigate(`/messages?c=${data}&u=${partnerId}`);
                                } catch (e) {
                                  toast({ title: 'Cannot message', description: 'You can only message accepted connections.', variant: 'destructive' });
                                }
                              }}>
                                Chat
                              </Button>
                            ) : null}
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              ) : (
                <Card>
                  <CardContent className="p-8 text-center">
                    <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No users found</h3>
                    <p className="text-muted-foreground">
                      Try searching with a different name or username.
                    </p>
                  </CardContent>
                </Card>
              )
            ) : (
              /* Show nearby people when not searching */
              <>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <MapPin className="h-5 w-5 text-primary" />
                      <span>People Nearby</span>
                    </CardTitle>
                  </CardHeader>
                </Card>
                
                {loadingNearby ? (
                  <Card>
                    <CardContent className="p-6 text-center">
                      <div className="animate-pulse flex items-center justify-center space-x-2">
                        <MapPin className="h-4 w-4 text-primary animate-bounce" />
                        <span>Finding people nearby...</span>
                      </div>
                    </CardContent>
                  </Card>
                ) : nearbyPeople.length > 0 ? (
                  <div className="space-y-3">
                    {nearbyPeople.map((profile) => {
                      const status = getConnectionStatus(profile);
                      return (
                        <Card key={profile.id}>
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-3">
                                <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                                  <span className="text-lg font-semibold text-primary">
                                    {profile.full_name.charAt(0)}
                                  </span>
                                </div>
                                <div>
                                  <div className="flex items-center space-x-2">
                                    <p className="font-medium">{profile.full_name}</p>
                                    <span className="text-muted-foreground">{" "}</span>
                                    {
                                      profile.distance_m !== null ? (
                                        <Badge variant="secondary" className="text-xs">
                                          {Math.round(profile.distance_m)} m
                                        </Badge>
                                      ) : (
                                        profile.distance_m === 0 ? (
                                          <span className="text-muted-foreground">0 m</span>
                                        ) : (
                                          <span className="text-muted-foreground">Unknown</span>
                                        )
                                      )
                                    }
                                  </div>
                                  <p className="text-sm text-muted-foreground">@{profile.username}</p>
                                  {profile.bio && (
                                    <p className="text-xs text-muted-foreground mt-1">{profile.bio}</p>
                                  )}
                                  {profile.interests && profile.interests.length > 0 && (
                                    <div className="flex flex-wrap gap-1 mt-2">
                                      {profile.interests.slice(0, 3).map((interest, idx) => (
                                        <Badge key={idx} variant="outline" className="text-xs">
                                          {interest}
                                        </Badge>
                                      ))}
                                    </div>
                                  )}
                                  {profile.is_business && (
                                    <Badge variant="outline" className="text-xs mt-1">
                                      Business
                                    </Badge>
                                  )}
                                </div>
                              </div>
                              {status === 'none' || status === 'rejected' ? (
                                <Button size="sm" onClick={() => sendConnectionRequest(profile.user_id)}>
                                  <UserPlus className="h-4 w-4 mr-2" />
                                </Button>
                              ) : status === 'pending' ? (
                                <Badge variant="secondary" className="text-xs">Pending</Badge>
                              ) : status === 'accepted' ? (
                                <Button size="sm" variant="outline" onClick={async () => {
                                  try {
                                    const partnerId = profile.user_id;
                                    const { data, error } = await supabase.rpc('get_or_create_conversation', { partner_id: partnerId });
                                    if (error) throw error;
                                    navigate(`/messages?c=${data}&u=${partnerId}`);
                                  } catch (e) {
                                    toast({ title: 'Cannot message', description: 'You can only message accepted connections.', variant: 'destructive' });
                                  }
                                }}>
                                  Chat
                                </Button>
                              ) : null}
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                ) : (
                  <Card>
                    <CardContent className="p-8 text-center">
                      <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No people nearby</h3>
                      <p className="text-muted-foreground mb-4">
                        We couldn't find any people in your area right now.
                      </p>
                      <Button 
                        variant="outline" 
                        onClick={fetchNearbyPeople}
                        disabled={loadingNearby}
                      >
                        <MapPin className="h-4 w-4 mr-2" />
                        Refresh
                      </Button>
                    </CardContent>
                  </Card>
                )}
              </>
            )}
          </TabsContent>
        </Tabs>
      </main>

      <MobileNavigation />
    </div>
  );
};

export default Connections;