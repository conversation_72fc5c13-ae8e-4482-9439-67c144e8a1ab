import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// Deep link handler for Supabase OAuth callback (Mobile only)
import { App as CapacitorApp } from '@capacitor/app';
import { Capacitor } from '@capacitor/core';
import { supabase } from '@/integrations/supabase/client';
import { StatusBarManager } from '@/utils/statusBar';
import { logger } from '@/utils/logger';

// Only set up deep link handler for mobile platforms
if (Capacitor.isNativePlatform()) {
    CapacitorApp.addListener('appUrlOpen', async ({ url }) => {
        if (url.includes('auth/callback')) {
            try {
                logger.log('Mobile deep link URL received:', url);

                // Parse URL parameters for mobile OAuth
                let params: URLSearchParams;

                try {
                    const urlObj = new URL(url);
                    // First try hash parameters (common for OAuth)
                    if (urlObj.hash) {
                        params = new URLSearchParams(urlObj.hash.substring(1));
                    } else {
                        // Fallback to query parameters
                        params = new URLSearchParams(urlObj.search);
                    }
                } catch (urlError) {
                    // If URL parsing fails, try manual parsing
                    logger.log('URL parsing failed, trying manual parsing');
                    const hashIndex = url.indexOf('#');
                    const queryIndex = url.indexOf('?');

                    if (hashIndex !== -1) {
                        params = new URLSearchParams(url.substring(hashIndex + 1));
                    } else if (queryIndex !== -1) {
                        params = new URLSearchParams(url.substring(queryIndex + 1));
                    } else {
                        throw new Error('No parameters found in URL');
                    }
                }

                const access_token = params.get('access_token');
                const refresh_token = params.get('refresh_token');
                const error_code = params.get('error_code');
                const error_description = params.get('error_description');

                console.log('Parsed mobile OAuth parameters:', {
                    access_token: access_token ? 'present' : 'missing',
                    refresh_token: refresh_token ? 'present' : 'missing',
                    error_code,
                    error_description
                });

                if (error_code) {
                    console.error('Mobile OAuth error:', error_description);
                    return;
                }

                if (access_token && refresh_token) {
                    // Set the session with the tokens
                    const { data, error } = await supabase.auth.setSession({
                        access_token,
                        refresh_token,
                    });

                    if (data) {
                        logger.log('Mobile OAuth session set successfully:', data);
                        // Force a reload to ensure the app state is updated
                        window.location.replace('/');
                    } else if (error) {
                        logger.error('Error setting mobile session:', error.message);
                    }
                } else {
                    logger.error('No access token or refresh token found in mobile URL');
                    logger.log('Full mobile URL for debugging:', url);
                }
            } catch (err) {
                logger.error('Error handling mobile deep link:', err);
            }
        }
    });
}

// Initialize mobile settings for edge-to-edge experience
if (Capacitor.isNativePlatform()) {
    // Initialize status bar for edge-to-edge experience
    StatusBarManager.initializeEdgeToEdge();
}

createRoot(document.getElementById("root")!).render(<App />);
