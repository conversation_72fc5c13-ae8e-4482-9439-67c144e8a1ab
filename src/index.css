@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    
    /* Nearby Connect specific colors */
    --location-primary: 142 76% 36%;
    --location-secondary: 142 70% 45%;
    --connection-active: 221.2 83.2% 53.3%;
    --nearby-radius: 210 40% 80%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 94.1%;
    --sidebar-primary-foreground: 240 5.9% 10%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    
    /* Nearby Connect specific colors - dark mode */
    --location-primary: 142 76% 50%;
    --location-secondary: 142 70% 60%;
    --connection-active: 217.2 91.2% 59.8%;
    --nearby-radius: 215 20.2% 35.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Mobile-specific styles */
@media (max-width: 768px) {
  .mobile-safe-area {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
}

/* Custom animations for mobile interactions */
@keyframes pulse-location {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

.pulse-location {
  animation: pulse-location 2s infinite;
}

/* Pulsing blue dot for user location on map */
.pulse-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #4285f4;
  box-shadow: 0 0 0 0 rgba(66,133,244, 0.7);
  animation: pulse-dot 1.5s infinite;
  border: 2px solid #fff;
  position: absolute;
  left: -8px;
  top: -8px;
}

@keyframes pulse-dot {
  0% {
    box-shadow: 0 0 0 0 rgba(66,133,244, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(66,133,244, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(66,133,244, 0);
  }
}

/* Edge-to-edge design with proper safe areas */
@supports (padding: max(0px)) {
  .safe-area-top {
    padding-top: max(env(safe-area-inset-top), 0px);
  }

  .safe-area-bottom {
    padding-bottom: max(env(safe-area-inset-bottom), 0px);
  }

  .safe-area-left {
    padding-left: max(env(safe-area-inset-left), 0px);
  }

  .safe-area-right {
    padding-right: max(env(safe-area-inset-right), 0px);
  }

  .safe-area-all {
    padding-top: max(env(safe-area-inset-top), 0px);
    padding-bottom: max(env(safe-area-inset-bottom), 0px);
    padding-left: max(env(safe-area-inset-left), 0px);
    padding-right: max(env(safe-area-inset-right), 0px);
  }
}

/* Edge-to-edge design for Android */
.capacitor-android {
  /* Enable edge-to-edge by extending behind system bars */
  padding-top: 0;
  padding-bottom: 0;

  /* Use viewport units that account for system bars */
  min-height: 100vh;
  min-height: 100dvh; /* Dynamic viewport height */
}

/* Utility classes for edge-to-edge content */
.edge-to-edge-content {
  /* Content that should extend behind system bars */
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

.edge-to-edge-top {
  /* Content that should extend behind status bar only */
  padding-top: env(safe-area-inset-top);
}

.edge-to-edge-bottom {
  /* Content that should extend behind navigation bar only */
  padding-bottom: env(safe-area-inset-bottom);
}



/* Ensure proper height on mobile */
html, body, #root {
  height: 100vh;
  height: 100dvh; /* Dynamic viewport height for mobile */
  overflow-x: hidden;
}

/* Hide scrollbars in native app mode */
.capacitor-android {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.capacitor-android::-webkit-scrollbar {
  display: none;
}