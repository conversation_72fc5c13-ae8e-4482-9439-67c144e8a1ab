/**
 * Environment-based logging utility
 * Only logs in development environment when environment=dev is set in .env
 */

// Check if we're in development environment
const isDevelopment = import.meta.env.VITE_ENVIRONMENT === 'dev';

/**
 * Logger utility that respects environment settings
 * All logging is suppressed in production
 */
export const logger = {
  /**
   * Log general information (only in development)
   */
  log: (...args: any[]) => {
    if (isDevelopment) {
      console.log(...args);
    }
  },

  /**
   * Log errors (only in development)
   */
  error: (...args: any[]) => {
    if (isDevelopment) {
      console.error(...args);
    }
  },

  /**
   * Log warnings (only in development)
   */
  warn: (...args: any[]) => {
    if (isDevelopment) {
      console.warn(...args);
    }
  },

  /**
   * Log debug information (only in development)
   */
  debug: (...args: any[]) => {
    if (isDevelopment) {
      console.debug(...args);
    }
  },

  /**
   * Log information with a specific prefix (only in development)
   */
  info: (...args: any[]) => {
    if (isDevelopment) {
      console.info(...args);
    }
  },

  /**
   * Group console logs (only in development)
   */
  group: (label: string) => {
    if (isDevelopment) {
      console.group(label);
    }
  },

  /**
   * End console group (only in development)
   */
  groupEnd: () => {
    if (isDevelopment) {
      console.groupEnd();
    }
  },

  /**
   * Log table data (only in development)
   */
  table: (data: any) => {
    if (isDevelopment) {
      console.table(data);
    }
  }
};

/**
 * Check if we're in development environment
 */
export const isDevEnvironment = () => isDevelopment;

/**
 * Conditional execution - only runs in development
 */
export const devOnly = (callback: () => void) => {
  if (isDevelopment) {
    callback();
  }
};

/**
 * Sanitize sensitive data for logging
 * Removes or masks sensitive information even in development
 */
export const sanitizeForLogging = (data: any): any => {
  if (!isDevelopment) {
    return '[REDACTED]';
  }

  if (typeof data !== 'object' || data === null) {
    return data;
  }

  const sensitiveKeys = [
    'password', 'token', 'secret', 'key', 'auth', 'credential',
    'api_key', 'access_token', 'refresh_token', 'session_token'
  ];

  const sanitized = { ...data };
  
  for (const key in sanitized) {
    if (sensitiveKeys.some(sensitive => 
      key.toLowerCase().includes(sensitive.toLowerCase())
    )) {
      sanitized[key] = '[MASKED]';
    } else if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
      sanitized[key] = sanitizeForLogging(sanitized[key]);
    }
  }

  return sanitized;
};

/**
 * Log API calls with sanitized data (only in development)
 */
export const logApiCall = (method: string, url: string, data?: any) => {
  if (isDevelopment) {
    logger.group(`🌐 API Call: ${method.toUpperCase()} ${url}`);
    if (data) {
      logger.log('Data:', sanitizeForLogging(data));
    }
    logger.groupEnd();
  }
};

/**
 * Log database operations (only in development)
 */
export const logDbOperation = (operation: string, table: string, data?: any) => {
  if (isDevelopment) {
    logger.group(`🗄️ DB Operation: ${operation} on ${table}`);
    if (data) {
      logger.log('Data:', sanitizeForLogging(data));
    }
    logger.groupEnd();
  }
};

/**
 * Log authentication events (only in development)
 */
export const logAuthEvent = (event: string, userId?: string, details?: any) => {
  if (isDevelopment) {
    logger.group(`🔐 Auth Event: ${event}`);
    if (userId) {
      // Only show first 8 characters of user ID for privacy
      logger.log('User ID:', userId.substring(0, 8) + '...');
    }
    if (details) {
      logger.log('Details:', sanitizeForLogging(details));
    }
    logger.groupEnd();
  }
};

export default logger;
