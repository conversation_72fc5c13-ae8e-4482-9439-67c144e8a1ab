import { Capacitor } from '@capacitor/core';
import { StatusBar, Style } from '@capacitor/status-bar';

export class StatusBarManager {
  private static currentStyle: Style = Style.Light;

  /**
   * Set status bar style based on content background
   */
  static async setStyle(style: Style): Promise<void> {
    if (!Capacitor.isNativePlatform()) {
      return;
    }

    try {
      await StatusBar.setStyle({ style });
      this.currentStyle = style;
      console.log(`Status bar style set to: ${style}`);
    } catch (error) {
      console.error('Error setting status bar style:', error);
    }
  }

  /**
   * Set status bar for light backgrounds (dark icons)
   */
  static async setLightBackground(): Promise<void> {
    await this.setStyle(Style.Dark);
  }

  /**
   * Set status bar for dark backgrounds (light icons)
   */
  static async setDarkBackground(): Promise<void> {
    await this.setStyle(Style.Light);
  }

  /**
   * Initialize edge-to-edge status bar
   */
  static async initializeEdgeToEdge(): Promise<void> {
    if (!Capacitor.isNativePlatform()) {
      return;
    }

    try {
      // Make status bar transparent and overlay
      await StatusBar.setBackgroundColor({ color: '#00000000' });
      await StatusBar.setOverlaysWebView({ overlay: true });
      await StatusBar.show();
      
      // Set initial style (can be changed dynamically)
      await this.setStyle(Style.Light);
      
      console.log('Edge-to-edge status bar initialized');
    } catch (error) {
      console.error('Error initializing edge-to-edge status bar:', error);
    }
  }

  /**
   * Set status bar color for specific sections
   */
  static async setBackgroundColor(color: string): Promise<void> {
    if (!Capacitor.isNativePlatform()) {
      return;
    }

    try {
      await StatusBar.setBackgroundColor({ color });
      console.log(`Status bar background color set to: ${color}`);
    } catch (error) {
      console.error('Error setting status bar background color:', error);
    }
  }

  /**
   * Hide status bar
   */
  static async hide(): Promise<void> {
    if (!Capacitor.isNativePlatform()) {
      return;
    }

    try {
      await StatusBar.hide();
      console.log('Status bar hidden');
    } catch (error) {
      console.error('Error hiding status bar:', error);
    }
  }

  /**
   * Show status bar
   */
  static async show(): Promise<void> {
    if (!Capacitor.isNativePlatform()) {
      return;
    }

    try {
      await StatusBar.show();
      console.log('Status bar shown');
    } catch (error) {
      console.error('Error showing status bar:', error);
    }
  }

  /**
   * Get current status bar style
   */
  static getCurrentStyle(): Style {
    return this.currentStyle;
  }

  /**
   * Auto-adjust status bar based on page background
   */
  static async autoAdjustForPage(backgroundColor: string): Promise<void> {
    if (!Capacitor.isNativePlatform()) {
      return;
    }

    // Simple logic to determine if background is light or dark
    const isLightBackground = this.isLightColor(backgroundColor);
    
    if (isLightBackground) {
      await this.setLightBackground(); // Dark icons on light background
    } else {
      await this.setDarkBackground(); // Light icons on dark background
    }
  }

  /**
   * Determine if a color is light or dark
   */
  private static isLightColor(color: string): boolean {
    // Remove # if present
    color = color.replace('#', '');
    
    // Convert to RGB
    const r = parseInt(color.substr(0, 2), 16);
    const g = parseInt(color.substr(2, 2), 16);
    const b = parseInt(color.substr(4, 2), 16);
    
    // Calculate luminance
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
    
    // Return true if light (luminance > 0.5)
    return luminance > 0.5;
  }
}
