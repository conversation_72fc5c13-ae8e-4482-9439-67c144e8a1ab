import { supabase } from '@/integrations/supabase/client';
import { logger } from './logger';

/**
 * Test utilities for the Nearby Connect feature
 * These functions help test the new location-based discovery functionality
 */

export const testNearbyConnectFeatures = async () => {
  logger.log('🧪 Testing Nearby Connect features with comprehensive validation...');

  let testsPassed = 0;
  let testsTotal = 0;

  try {
    // Test 1: Update user location
    testsTotal++;
    logger.log('📍 Test 1: Location update...');
    const locationResult = await supabase.rpc('update_user_location', {
      lat: 28.6677, // Delhi coordinates for testing
      lng: 77.4337,
      accuracy: 10
    });

    if (locationResult.error) {
      logger.error('❌ Location update failed:', locationResult.error);
      logger.error('Error details:', {
        code: locationResult.error.code,
        message: locationResult.error.message,
        details: locationResult.error.details
      });
    } else {
      logger.log('✅ Location updated successfully:', locationResult.data);
      testsPassed++;
    }

    // Test 2: Update interests
    testsTotal++;
    logger.log('🎯 Test 2: Interests update...');
    const interestsResult = await supabase.rpc('update_user_interests', {
      new_interests: ['hiking', 'photography', 'coffee', 'tech', 'travel']
    });

    if (interestsResult.error) {
      logger.error('❌ Interests update failed:', interestsResult.error);
      logger.error('Error details:', {
        code: interestsResult.error.code,
        message: interestsResult.error.message
      });
    } else {
      logger.log('✅ Interests updated successfully:', interestsResult.data);
      testsPassed++;
    }

    // Test 3: Update visibility status
    testsTotal++;
    logger.log('👁️ Test 3: Visibility toggle...');
    const visibilityResult = await supabase.rpc('toggle_visibility_status', {
      new_status: 'visible'
    });

    if (visibilityResult.error) {
      logger.error('❌ Visibility update failed:', visibilityResult.error);
      logger.error('Error details:', {
        code: visibilityResult.error.code,
        message: visibilityResult.error.message
      });
    } else {
      logger.log('✅ Visibility updated successfully:', visibilityResult.data);
      testsPassed++;
    }

    // Test 4: Update online presence
    testsTotal++;
    logger.log('🟢 Test 4: Online presence...');
    const presenceResult = await supabase.rpc('update_online_presence', {
      is_online: true
    });

    if (presenceResult.error) {
      logger.error('❌ Presence update failed:', presenceResult.error);
      logger.error('Error details:', {
        code: presenceResult.error.code,
        message: presenceResult.error.message
      });
    } else {
      logger.log('✅ Presence updated successfully:', presenceResult.data);
      testsPassed++;
    }

    // Test 5: Distance calculation
    testsTotal++;
    logger.log('📏 Test 5: Distance calculation...');
    const distanceResult = await supabase.rpc('calculate_distance', {
      lat1: 28.6677,
      lng1: 77.4337,
      lat2: 28.7041,
      lng2: 77.1025
    });

    if (distanceResult.error) {
      logger.error('❌ Distance calculation failed:', distanceResult.error);
    } else {
      logger.log('✅ Distance calculated successfully:', `${distanceResult.data} km`);
      testsPassed++;
    }

    // Test 6: Find nearby users
    testsTotal++;
    logger.log('🔍 Test 6: Nearby users search...');
    const nearbyResult = await supabase.rpc('find_nearby_users', {
      user_lat: 28.6677,
      user_lng: 77.4337,
      radius_km: 10,
      interest_filter: ['tech', 'coffee'],
      include_offline: true
    });

    if (nearbyResult.error) {
      logger.error('❌ Nearby search failed:', nearbyResult.error);
      logger.error('Error details:', {
        code: nearbyResult.error.code,
        message: nearbyResult.error.message,
        hint: nearbyResult.error.hint
      });
    } else {
      logger.log('✅ Nearby search successful:', nearbyResult.data);
      logger.log(`Found ${nearbyResult.data?.length || 0} nearby users`);
      if (nearbyResult.data && nearbyResult.data.length > 0) {
        logger.log('Sample user data:', nearbyResult.data[0]);
      }
      testsPassed++;
    }

    // Test 7: Edge case - Invalid coordinates
    testsTotal++;
    logger.log('⚠️ Test 7: Invalid coordinates (should fail gracefully)...');
    const invalidLocationResult = await supabase.rpc('update_user_location', {
      lat: 999, // Invalid latitude
      lng: 77.4337,
      accuracy: 10
    });

    if (invalidLocationResult.error) {
      logger.log('✅ Invalid coordinates properly rejected:', invalidLocationResult.error.message);
      testsPassed++;
    } else {
      logger.error('❌ Invalid coordinates were accepted (this should not happen)');
    }

    // Summary
    logger.log(`\n🎯 Test Results: ${testsPassed}/${testsTotal} tests passed`);

    if (testsPassed === testsTotal) {
      logger.log('🎉 All tests passed! Nearby Connect is ready for production.');
      return true;
    } else {
      logger.log('⚠️ Some tests failed. Please review the errors above.');
      return false;
    }

  } catch (error) {
    logger.error('❌ Test suite crashed:', error);
    return false;
  }
};

export const debugNearbyConnectSetup = async () => {
  logger.log('🔧 Debugging Nearby Connect setup...');
  
  try {
    // Check current user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .single();
    
    if (profileError) {
      logger.error('❌ Profile fetch failed:', profileError);
      return;
    }
    
    logger.log('👤 Current profile:', {
      id: profile.id,
      name: profile.name,
      latitude: profile.latitude,
      longitude: profile.longitude,
      interests: profile.interests,
      visibility_status: profile.visibility_status,
      online_status: profile.online_status,
      location_updated_at: profile.location_updated_at
    });

    // Check if profiles table is in realtime publication
    const { data: publications } = await supabase
      .from('pg_publication_tables')
      .select('*')
      .eq('pubname', 'supabase_realtime')
      .eq('tablename', 'profiles');
    
    logger.log('📡 Profiles in realtime publication:', publications);

    return profile;
    
  } catch (error) {
    logger.error('❌ Debug failed:', error);
  }
};

// Helper to format distance for display
export const formatDistance = (km: number): string => {
  if (km < 1) {
    return `${Math.round(km * 1000)}m away`;
  }
  return `${km.toFixed(1)}km away`;
};

// Helper to format last seen time
export const formatLastSeen = (lastSeen: string): string => {
  const date = new Date(lastSeen);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
  
  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
  return `${Math.floor(diffInMinutes / 1440)}d ago`;
};

// Helper to check if user has required location permissions
export const checkLocationPermissions = async (): Promise<boolean> => {
  if (!navigator.geolocation) {
    logger.error('❌ Geolocation not supported');
    return false;
  }

  try {
    const permission = await navigator.permissions.query({ name: 'geolocation' });
    logger.log('📍 Location permission status:', permission.state);
    return permission.state === 'granted';
  } catch (error) {
    logger.error('❌ Permission check failed:', error);
    return false;
  }
};

// Production-ready validation functions
export const validateCoordinates = (lat: number, lng: number): boolean => {
  return lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;
};

export const validateRadius = (radius: number): boolean => {
  return radius > 0 && radius <= 100; // Max 100km radius
};

export const validateInterests = (interests: string[]): { valid: boolean; error?: string } => {
  if (!Array.isArray(interests)) {
    return { valid: false, error: 'Interests must be an array' };
  }

  if (interests.length > 10) {
    return { valid: false, error: 'Maximum 10 interests allowed' };
  }

  for (const interest of interests) {
    if (typeof interest !== 'string') {
      return { valid: false, error: 'All interests must be strings' };
    }

    if (interest.trim().length === 0) {
      return { valid: false, error: 'Empty interests are not allowed' };
    }

    if (interest.length > 50) {
      return { valid: false, error: 'Each interest must be 50 characters or less' };
    }
  }

  return { valid: true };
};

// Enhanced error handling for RPC calls
export const handleRpcError = (error: any, operation: string) => {
  logger.error(`❌ ${operation} failed:`, error);

  // Map database error codes to user-friendly messages
  const errorMessages: Record<string, string> = {
    'UNAUTHENTICATED': 'Please sign in to continue',
    'INVALID_PARAMETER_VALUE': 'Invalid input provided',
    'TOO_MANY_REQUESTS': 'Please wait before trying again',
    'NO_DATA_FOUND': 'Profile not found',
    'PRECONDITION_FAILED': 'Please update your location first'
  };

  const userMessage = errorMessages[error.code] || error.message || 'An unexpected error occurred';

  return {
    success: false,
    error: userMessage,
    code: error.code
  };
};

// Safe wrapper for RPC calls with validation
export const safeRpcCall = async (
  rpcName: string,
  params: any,
  validator?: (params: any) => { valid: boolean; error?: string }
) => {
  try {
    // Validate parameters if validator provided
    if (validator) {
      const validation = validator(params);
      if (!validation.valid) {
        return {
          success: false,
          error: validation.error,
          data: null
        };
      }
    }

    const { data, error } = await supabase.rpc(rpcName, params);

    if (error) {
      return handleRpcError(error, rpcName);
    }

    return {
      success: true,
      data,
      error: null
    };

  } catch (error) {
    return handleRpcError(error, rpcName);
  }
};
