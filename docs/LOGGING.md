# Environment-Based Logging System

This project implements a comprehensive environment-based logging system that automatically suppresses all debug output in production while providing detailed logging in development.

## Configuration

### Environment Variable

Set the `environment` variable in your `.env` file:

```bash
# Development mode - enables all logging
environment=dev

# Production mode - suppresses all logging (or omit the variable)
environment=prod
```

### Vite Configuration

The environment variable is exposed to the client through Vite configuration in `vite.config.ts`:

```typescript
define: {
  'import.meta.env.VITE_ENVIRONMENT': JSON.stringify(env.environment),
}
```

## Usage

### Basic Logging

Import and use the logger instead of console methods:

```typescript
import { logger } from '@/utils/logger';

// These only log in development
logger.log('General information');
logger.error('Error message');
logger.warn('Warning message');
logger.debug('Debug information');
logger.info('Info message');
```

### Specialized Logging

Use specialized logging functions for different types of operations:

```typescript
import { logApiCall, logDbOperation, logAuthEvent } from '@/utils/logger';

// API calls
logApiCall('GET', '/api/users', { userId: '123' });

// Database operations
logDbOperation('SELECT', 'users', { where: 'id = 123' });

// Authentication events
logAuthEvent('login', 'user123', { method: 'google' });
```

### Conditional Execution

Use `devOnly` for code that should only run in development:

```typescript
import { devOnly } from '@/utils/logger';

devOnly(() => {
  // This code only runs in development
  performExpensiveDebugging();
});
```

### Data Sanitization

The logger automatically sanitizes sensitive data:

```typescript
import { sanitizeForLogging } from '@/utils/logger';

const userData = {
  username: 'john',
  password: 'secret123',  // Will be masked
  api_key: 'abc123',      // Will be masked
  email: '<EMAIL>'
};

logger.log('User data:', sanitizeForLogging(userData));
// Output: { username: 'john', password: '[MASKED]', api_key: '[MASKED]', email: '<EMAIL>' }
```

### Environment Check

Check if you're in development mode:

```typescript
import { isDevEnvironment } from '@/utils/logger';

if (isDevEnvironment()) {
  // Development-only code
}
```

## Components

### Debug Components

Components like `DebugLocationInfo` automatically hide themselves in production:

```typescript
const DebugComponent = () => {
  // Only render in development
  if (!isDevEnvironment()) {
    return null;
  }
  
  return <div>Debug information</div>;
};
```

## Security Features

1. **Automatic Suppression**: All logging is completely suppressed in production
2. **Data Sanitization**: Sensitive keys are automatically masked even in development
3. **No Performance Impact**: Logging calls are no-ops in production
4. **Environment Isolation**: Debug components don't render in production

## Sensitive Data Protection

The following keys are automatically masked in logs:
- `password`
- `token`
- `secret`
- `key`
- `auth`
- `credential`
- `api_key`
- `access_token`
- `refresh_token`
- `session_token`

## Testing

Use the test utilities to verify logging behavior:

```typescript
import { testLogger } from '@/utils/test-logger';

// Run comprehensive logging tests
testLogger();
```

Or use the `LoggerTest` component during development to interactively test logging.

## Migration from console.*

Replace all instances of:
- `console.log()` → `logger.log()`
- `console.error()` → `logger.error()`
- `console.warn()` → `logger.warn()`
- `console.debug()` → `logger.debug()`
- `console.info()` → `logger.info()`

## Best Practices

1. Always use the logger instead of direct console methods
2. Use specialized logging functions for API, DB, and auth operations
3. Wrap debug-only components with environment checks
4. Use `devOnly()` for expensive debug operations
5. Let the sanitization system handle sensitive data automatically

## Production Deployment

When deploying to production:
1. Set `environment=prod` or remove the environment variable
2. All logging will be automatically suppressed
3. Debug components will not render
4. No performance impact from logging calls
