import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.rawatinnovations.nearbyconnect',
  appName: 'NearbyConnect',
  webDir: 'dist',
  server: {
    url: 'https://nearbyconnect-v004.vercel.app',
    cleartext: true,
  },

  android: {
    allowMixedContent: true,
    captureInput: true,
    webContentsDebuggingEnabled: true,
  },
  plugins: {
    StatusBar: {
      style: 'LIGHT',
      backgroundColor: '#00000000', // Transparent for edge-to-edge
      overlay: true, // Enable overlay for edge-to-edge
    },
    SplashScreen: {
      launchShowDuration: 2000,
      backgroundColor: '#FFFFFF',
      showSpinner: false,
    },
  },
};

export default config;
