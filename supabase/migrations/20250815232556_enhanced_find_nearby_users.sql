-- Enhanced find_nearby_users function for map-based discovery
-- Supports radius filtering, interest matching, and distance calculations

-- 1. Haversine distance calculation function
create or replace function public.calculate_distance(
  lat1 decimal, lng1 decimal,
  lat2 decimal, lng2 decimal
)
returns decimal
language plpgsql
immutable
as $$
declare
  r decimal := 6371; -- Earth's radius in kilometers
  dlat decimal;
  dlng decimal;
  a decimal;
  c decimal;
begin
  dlat := radians(lat2 - lat1);
  dlng := radians(lng2 - lng1);

  a := sin(dlat/2) * sin(dlat/2) +
       cos(radians(lat1)) * cos(radians(lat2)) *
       sin(dlng/2) * sin(dlng/2);

  c := 2 * atan2(sqrt(a), sqrt(1-a));

  return r * c;
end;
$$;

-- 2. Enhanced find_nearby_users function
create or replace function public.find_nearby_users(
  user_lat decimal default null,
  user_lng decimal default null,
  radius_km integer default 5,
  interest_filter text[] default null,
  include_offline boolean default true
)
returns table (
  id uuid,
  name text,
  avatar_url text,
  bio text,
  interests text[],
  latitude decimal,
  longitude decimal,
  distance_km decimal,
  online_status boolean,
  last_seen timestamptz,
  connection_status text,
  shared_interests text[]
)
language plpgsql
security definer
set search_path = public
as $$
declare
  current_user_id uuid := auth.uid();
  user_location record;
begin
  if current_user_id is null then
    raise exception 'Not authenticated';
  end if;

  -- Get user's location if not provided
  if user_lat is null or user_lng is null then
    select latitude, longitude into user_location
    from public.profiles
    where id = current_user_id;

    if user_location.latitude is null or user_location.longitude is null then
      raise exception 'User location not available. Please update your location first.';
    end if;

    user_lat := user_location.latitude;
    user_lng := user_location.longitude;
  end if;

  return query
  select
    p.id,
    p.name,
    p.avatar_url,
    p.bio,
    p.interests,
    p.latitude,
    p.longitude,
    public.calculate_distance(user_lat, user_lng, p.latitude, p.longitude) as distance_km,
    p.online_status,
    p.last_seen,
    coalesce(c.status, 'none') as connection_status,
    case
      when interest_filter is not null then
        array(select unnest(p.interests) intersect select unnest(interest_filter))
      else
        array[]::text[]
    end as shared_interests
  from public.profiles p
  left join public.connections c on (
    (c.requester_id = current_user_id and c.receiver_id = p.id) or
    (c.requester_id = p.id and c.receiver_id = current_user_id)
  )
  where
    p.id != current_user_id
    and p.latitude is not null
    and p.longitude is not null
    and p.visibility_status = 'visible'
    and (include_offline or p.online_status = true)
    and public.calculate_distance(user_lat, user_lng, p.latitude, p.longitude) <= radius_km
    and (
      interest_filter is null
      or p.interests && interest_filter  -- Array overlap operator
    )
  order by distance_km asc
  limit 100; -- Reasonable limit for map display
end;
$$;

-- 3. Function to get user distance from specific coordinates
create or replace function public.get_user_distance(
  target_user_id uuid,
  from_lat decimal,
  from_lng decimal
)
returns decimal
language plpgsql
security definer
set search_path = public
as $$
declare
  target_lat decimal;
  target_lng decimal;
begin
  if auth.uid() is null then
    raise exception 'Not authenticated';
  end if;

  select latitude, longitude into target_lat, target_lng
  from public.profiles
  where id = target_user_id
    and visibility_status = 'visible'
    and latitude is not null
    and longitude is not null;

  if target_lat is null then
    return null;
  end if;

  return public.calculate_distance(from_lat, from_lng, target_lat, target_lng);
end;
$$;

-- 4. Function to update online presence
create or replace function public.update_online_presence(is_online boolean default true)
returns json
language plpgsql
security definer
set search_path = public
as $$
declare
  user_id uuid := auth.uid();
  result json;
begin
  if user_id is null then
    raise exception 'Not authenticated';
  end if;

  update public.profiles
  set
    online_status = is_online,
    last_seen = now()
  where id = user_id;

  select json_build_object(
    'success', true,
    'online_status', is_online,
    'last_seen', now()
  ) into result;

  return result;
end;
$$;

-- 5. Add real-time publication for profiles table
do $$
begin
  begin
    alter publication supabase_realtime add table public.profiles;
  exception when duplicate_object then null;
  end;
end $$;