-- Enhance profiles table for Nearby Connect feature
-- Adds location, interests, visibility, and presence tracking

-- 1. Add location fields
alter table public.profiles
  add column if not exists latitude decimal(10, 8),
  add column if not exists longitude decimal(11, 8),
  add column if not exists location_updated_at timestamptz,
  add column if not exists location_accuracy float;

-- 2. Add interests and discovery settings
alter table public.profiles
  add column if not exists interests text[] default '{}',
  add column if not exists bio text,
  add column if not exists discovery_radius integer default 2000; -- meters

-- 3. Add visibility and presence tracking
do $$
begin
  if not exists (select 1 from pg_type where typname = 'visibility_status') then
    create type visibility_status as enum ('visible', 'hidden', 'friends_only');
  end if;
end $$;

alter table public.profiles
  add column if not exists visibility_status visibility_status default 'visible',
  add column if not exists online_status boolean default false,
  add column if not exists last_seen timestamptz default now();

-- 4. Create indexes for efficient location queries
create index if not exists idx_profiles_location on public.profiles(latitude, longitude)
  where latitude is not null and longitude is not null and visibility_status = 'visible';

create index if not exists idx_profiles_location_updated on public.profiles(location_updated_at);
create index if not exists idx_profiles_interests on public.profiles using gin(interests);
create index if not exists idx_profiles_visibility on public.profiles(visibility_status);
create index if not exists idx_profiles_online_status on public.profiles(online_status, last_seen);

-- 5. Add location update function with rate limiting
create or replace function public.update_user_location(
  lat decimal(10, 8),
  lng decimal(11, 8),
  accuracy float default null
)
returns json
language plpgsql
security definer
set search_path = public
as $$
declare
  user_id uuid := auth.uid();
  last_update timestamptz;
  result json;
begin
  if user_id is null then
    raise exception 'Not authenticated';
  end if;

  -- Rate limiting: max once per 30 seconds
  select location_updated_at into last_update
  from public.profiles
  where id = user_id;

  if last_update is not null and last_update > now() - interval '30 seconds' then
    raise exception 'Location updates are rate limited to once per 30 seconds';
  end if;

  -- Validate coordinates
  if lat < -90 or lat > 90 or lng < -180 or lng > 180 then
    raise exception 'Invalid coordinates';
  end if;

  -- Update location
  update public.profiles
  set
    latitude = lat,
    longitude = lng,
    location_updated_at = now(),
    location_accuracy = accuracy,
    last_seen = now(),
    online_status = true
  where id = user_id;

  -- Return success with updated location
  select json_build_object(
    'success', true,
    'latitude', lat,
    'longitude', lng,
    'updated_at', now()
  ) into result;

  return result;
end;
$$;

-- 6. Add visibility toggle function
create or replace function public.toggle_visibility_status(new_status visibility_status)
returns json
language plpgsql
security definer
set search_path = public
as $$
declare
  user_id uuid := auth.uid();
  result json;
begin
  if user_id is null then
    raise exception 'Not authenticated';
  end if;

  update public.profiles
  set visibility_status = new_status
  where id = user_id;

  select json_build_object(
    'success', true,
    'visibility_status', new_status
  ) into result;

  return result;
end;
$$;

-- 7. Add interests update function
create or replace function public.update_user_interests(new_interests text[])
returns json
language plpgsql
security definer
set search_path = public
as $$
declare
  user_id uuid := auth.uid();
  result json;
begin
  if user_id is null then
    raise exception 'Not authenticated';
  end if;

  -- Validate interests (max 10 interests, each max 50 chars)
  if array_length(new_interests, 1) > 10 then
    raise exception 'Maximum 10 interests allowed';
  end if;

  -- Clean and validate each interest
  for i in 1..coalesce(array_length(new_interests, 1), 0) loop
    if length(trim(new_interests[i])) > 50 then
      raise exception 'Each interest must be 50 characters or less';
    end if;
    new_interests[i] := trim(new_interests[i]);
  end loop;

  update public.profiles
  set interests = new_interests
  where id = user_id;

  select json_build_object(
    'success', true,
    'interests', new_interests
  ) into result;

  return result;
end;
$$;