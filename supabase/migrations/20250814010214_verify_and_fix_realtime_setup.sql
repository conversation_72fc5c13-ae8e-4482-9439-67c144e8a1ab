-- Verify and fix realtime setup for messaging
-- This migration ensures all necessary components are properly configured for real-time messaging

-- 1) Ensure all messaging tables are in the realtime publication
-- Use DO blocks to handle potential "already exists" errors gracefully
do $$
begin
  -- Try to add tables to publication, ignore if they already exist
  begin
    alter publication supabase_realtime add table public.messages;
  exception when duplicate_object then
    -- Table already in publication, that's fine
  end;

  begin
    alter publication supabase_realtime add table public.conversations;
  exception when duplicate_object then
    -- Table already in publication, that's fine
  end;

  begin
    alter publication supabase_realtime add table public.conversation_participants;
  exception when duplicate_object then
    -- Table already in publication, that's fine
  end;
end $$;

-- 2) Ensure REPLICA IDENTITY is set correctly for real-time updates
-- This is crucial for real-time subscriptions to work properly
alter table public.messages replica identity full;
alter table public.conversations replica identity full;
alter table public.conversation_participants replica identity full;

-- 3) Create a simple test function to verify real-time is working
create or replace function public.test_realtime_insert(test_conversation_id uuid, test_content text)
returns uuid
language plpgsql
security definer
set search_path = public
as $$
declare
  message_id uuid;
begin
  -- This function can be called to test if real-time subscriptions are working
  -- It bypasses RLS to ensure the insert succeeds
  set local row_security = off;

  insert into public.messages (conversation_id, sender_id, receiver_id, content, is_read)
  values (test_conversation_id, auth.uid(), auth.uid(), test_content, false)
  returning id into message_id;

  return message_id;
end;
$$;

-- 4) Grant necessary permissions for real-time to work
grant usage on schema public to anon, authenticated;
grant select on public.messages to anon, authenticated;
grant select on public.conversations to anon, authenticated;
grant select on public.conversation_participants to anon, authenticated;

-- Note: The above grants are for real-time subscriptions to work.
-- The actual data access is still controlled by RLS policies.