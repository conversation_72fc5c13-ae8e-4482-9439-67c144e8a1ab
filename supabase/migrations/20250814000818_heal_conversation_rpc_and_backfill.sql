-- Heal conversations and harden RPC so <PERSON><PERSON> insert works reliably
-- 1) Backfill: ensure every conversation has exactly two participants based on messages
-- 2) Harden RPC: always ensure both participants exist inside a SECURITY DEFINER block with row_security off

-- 1) Backfill participants for existing conversations using messages table as truth
-- This runs with the current role (migration runner, service role in prod), so it can bypass RLS.
do $$
begin
  -- Insert missing participants inferred from messages.sender_id
  insert into public.conversation_participants (conversation_id, user_id)
  select distinct m.conversation_id, m.sender_id
  from public.messages m
  left join public.conversation_participants cp
    on cp.conversation_id = m.conversation_id and cp.user_id = m.sender_id
  where m.conversation_id is not null and cp.user_id is null;

  -- Insert missing participants inferred from messages.receiver_id
  insert into public.conversation_participants (conversation_id, user_id)
  select distinct m.conversation_id, m.receiver_id
  from public.messages m
  left join public.conversation_participants cp
    on cp.conversation_id = m.conversation_id and cp.user_id = m.receiver_id
  where m.conversation_id is not null and cp.user_id is null;
end $$;

-- 2) Harden RPC: get_or_create_conversation ensures both participants exist and connection is accepted
create or replace function public.get_or_create_conversation(partner_id uuid)
returns uuid
language plpgsql
security definer
set search_path = public
as $$
declare
  me uuid := auth.uid();
  conv_id uuid;
begin
  if me is null then
    raise exception 'Not authenticated';
  end if;

  -- Ensure accepted connection exists between me and partner
  if not exists (
    select 1 from public.connections c
    where c.status = 'accepted'
      and ((c.requester_id = me and c.receiver_id = partner_id)
        or (c.requester_id = partner_id and c.receiver_id = me))
  ) then
    raise exception 'No accepted connection between users';
  end if;

  -- Try to find existing conversation for this pair (order-agnostic)
  select c.id into conv_id
  from public.conversations c
  where exists (select 1 from public.conversation_participants p where p.conversation_id = c.id and p.user_id = me)
    and exists (select 1 from public.conversation_participants p where p.conversation_id = c.id and p.user_id = partner_id)
  limit 1;

  if conv_id is null then
    -- Create new conversation
    set local row_security = off;
    insert into public.conversations default values returning id into conv_id;
    insert into public.conversation_participants (conversation_id, user_id)
    values (conv_id, me), (conv_id, partner_id)
    on conflict do nothing; -- idempotent
  else
    -- Ensure both participant rows exist
    set local row_security = off;
    insert into public.conversation_participants (conversation_id, user_id)
    values (conv_id, me) on conflict do nothing;
    insert into public.conversation_participants (conversation_id, user_id)
    values (conv_id, partner_id) on conflict do nothing;
  end if;

  return conv_id;
end;
$$;