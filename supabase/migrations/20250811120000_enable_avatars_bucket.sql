-- Create a public 'avatars' storage bucket if not exists
insert into storage.buckets (id, name, public) values ('avatars', 'avatars', true) on conflict do nothing;

-- Policy: Only allow users to upload/delete their own avatar
create policy "Users can upload their own avatars" on storage.objects
  for insert to authenticated
  with check (bucket_id = 'avatars' and auth.uid()::text = split_part(name, '.', 1));

create policy "Users can delete their own avatars" on storage.objects
  for delete to authenticated
  using (bucket_id = 'avatars' and auth.uid()::text = split_part(name, '.', 1));

-- Policy: Anyone can read avatars (public bucket)
create policy "Anyone can read avatars" on storage.objects
  for select using (bucket_id = 'avatars');
