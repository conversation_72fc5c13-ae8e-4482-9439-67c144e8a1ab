drop policy "cp_insert_none" on "public"."conversation_participants";

drop policy "cp_select_if_conversation_participant" on "public"."conversation_participants";

drop policy "conv_insert_none" on "public"."conversations";

drop policy "conv_select_if_participant" on "public"."conversations";

drop policy "msg_insert_if_participant_and_connected" on "public"."messages";

drop policy "msg_select_if_participant" on "public"."messages";

drop policy "msg_update_if_participant_receiver" on "public"."messages";

revoke delete on table "public"."conversation_participants" from "anon";

revoke insert on table "public"."conversation_participants" from "anon";

revoke references on table "public"."conversation_participants" from "anon";

revoke select on table "public"."conversation_participants" from "anon";

revoke trigger on table "public"."conversation_participants" from "anon";

revoke truncate on table "public"."conversation_participants" from "anon";

revoke update on table "public"."conversation_participants" from "anon";

revoke delete on table "public"."conversation_participants" from "authenticated";

revoke insert on table "public"."conversation_participants" from "authenticated";

revoke references on table "public"."conversation_participants" from "authenticated";

revoke trigger on table "public"."conversation_participants" from "authenticated";

revoke truncate on table "public"."conversation_participants" from "authenticated";

revoke update on table "public"."conversation_participants" from "authenticated";

revoke delete on table "public"."conversations" from "anon";

revoke insert on table "public"."conversations" from "anon";

revoke references on table "public"."conversations" from "anon";

revoke select on table "public"."conversations" from "anon";

revoke trigger on table "public"."conversations" from "anon";

revoke truncate on table "public"."conversations" from "anon";

revoke update on table "public"."conversations" from "anon";

revoke delete on table "public"."conversations" from "authenticated";

revoke insert on table "public"."conversations" from "authenticated";

revoke references on table "public"."conversations" from "authenticated";

revoke trigger on table "public"."conversations" from "authenticated";

revoke truncate on table "public"."conversations" from "authenticated";

revoke update on table "public"."conversations" from "authenticated";

revoke delete on table "public"."messages" from "anon";

revoke insert on table "public"."messages" from "anon";

revoke references on table "public"."messages" from "anon";

revoke select on table "public"."messages" from "anon";

revoke trigger on table "public"."messages" from "anon";

revoke truncate on table "public"."messages" from "anon";

revoke update on table "public"."messages" from "anon";

revoke delete on table "public"."messages" from "authenticated";

revoke references on table "public"."messages" from "authenticated";

revoke trigger on table "public"."messages" from "authenticated";

revoke truncate on table "public"."messages" from "authenticated";

revoke update on table "public"."messages" from "authenticated";

alter table "public"."conversation_participants" enable row level security;

alter table "public"."conversations" enable row level security;

alter table "public"."messages" enable row level security;

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.is_realtime_internal()
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
begin
  -- Supabase Realtime uses service_role to access the database
  return auth.role() = 'service_role';
end;
$function$
;

CREATE OR REPLACE FUNCTION public.security_audit_check()
 RETURNS TABLE(table_name text, rls_enabled boolean, policy_count bigint, security_status text)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
begin
  return query
  select
    t.tablename::text,
    t.rowsecurity as rls_enabled,
    coalesce(p.policy_count, 0) as policy_count,
    case
      when t.rowsecurity and coalesce(p.policy_count, 0) > 0 then 'SECURE'
      when t.rowsecurity and coalesce(p.policy_count, 0) = 0 then 'RLS_ENABLED_NO_POLICIES'
      else 'VULNERABLE_RLS_DISABLED'
    end as security_status
  from pg_tables t
  left join (
    select
      tablename,
      count(*) as policy_count
    from pg_policies
    where schemaname = 'public'
    group by tablename
  ) p on p.tablename = t.tablename
  where t.schemaname = 'public'
  and t.tablename in ('messages', 'conversations', 'conversation_participants', 'profiles')
  order by t.tablename;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.test_realtime_comprehensive()
 RETURNS TABLE(test_category text, test_name text, status text, details text)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
begin
  -- Test 1: RLS Status
  return query
  select
    'RLS'::text,
    'Messages RLS'::text,
    case when pg_tables.rowsecurity then 'ENABLED' else 'DISABLED' end::text,
    'Row Level Security status'::text
  from pg_tables
  where schemaname = 'public' and tablename = 'messages';

  -- Test 2: Policy Count
  return query
  select
    'RLS'::text,
    'Policy Count'::text,
    count(*)::text,
    'Number of SELECT policies on messages'::text
  from pg_policies
  where schemaname = 'public' and tablename = 'messages' and cmd = 'SELECT';

  -- Test 3: Publication Status
  return query
  select
    'Publication'::text,
    'Messages in Publication'::text,
    case when exists(
      select 1 from pg_publication_rel pr
      join pg_class c on pr.prrelid = c.oid
      join pg_namespace n on c.relnamespace = n.oid
      join pg_publication p on pr.prpubid = p.oid
      where p.pubname = 'supabase_realtime'
      and n.nspname = 'public'
      and c.relname = 'messages'
    ) then 'YES' else 'NO' end::text,
    'Messages table in supabase_realtime publication'::text;

  -- Test 4: Replica Identity
  return query
  select
    'Replica'::text,
    'Messages Replica Identity'::text,
    case c.relreplident
      when 'f' then 'FULL'
      when 'd' then 'DEFAULT'
      when 'n' then 'NOTHING'
      when 'i' then 'INDEX'
      else 'UNKNOWN'
    end::text,
    'Replica identity setting for messages table'::text
  from pg_class c
  join pg_namespace n on c.relnamespace = n.oid
  where n.nspname = 'public' and c.relname = 'messages';

  -- Test 5: Permissions
  return query
  select
    'Permissions'::text,
    'Service Role Grants'::text,
    case when exists(
      select 1 from information_schema.table_privileges
      where table_schema = 'public'
      and table_name = 'messages'
      and grantee = 'service_role'
      and privilege_type = 'SELECT'
    ) then 'GRANTED' else 'NOT_GRANTED' end::text,
    'SELECT permission for service_role'::text;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.test_realtime_security()
 RETURNS TABLE(test_name text, result text, details text)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
begin
  return query
  select
    'RLS Status'::text,
    case when pg_tables.rowsecurity then 'ENABLED' else 'DISABLED' end::text,
    'Messages table RLS status'::text
  from pg_tables
  where schemaname = 'public' and tablename = 'messages'

  union all

  select
    'Policy Count'::text,
    count(*)::text,
    'Number of policies on messages table'::text
  from pg_policies
  where schemaname = 'public' and tablename = 'messages'

  union all

  select
    'Realtime Publication'::text,
    case when exists(
      select 1 from pg_publication_rel pr
      join pg_class c on pr.prrelid = c.oid
      join pg_namespace n on c.relnamespace = n.oid
      join pg_publication p on pr.prpubid = p.oid
      where p.pubname = 'supabase_realtime'
      and n.nspname = 'public'
      and c.relname = 'messages'
    ) then 'INCLUDED' else 'NOT_INCLUDED' end::text,
    'Messages table in realtime publication'::text;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.handle_new_user()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  user_full_name TEXT;
  user_username TEXT;
  user_email TEXT;
BEGIN
  -- Extract email
  user_email := COALESCE(NEW.email, '');
  
  -- Extract full name from various possible fields in Google OAuth
  user_full_name := COALESCE(
    NEW.raw_user_meta_data->>'full_name',
    NEW.raw_user_meta_data->>'name',
    SPLIT_PART(user_email, '@', 1),
    'User'
  );
  
  -- Generate username from various sources
  user_username := COALESCE(
    NEW.raw_user_meta_data->>'preferred_username',
    NEW.raw_user_meta_data->>'username',
    SPLIT_PART(user_email, '@', 1),
    'user_' || SUBSTRING(NEW.id::text, 1, 8)
  );
  
  -- Ensure username is not empty
  IF user_username = '' OR user_username IS NULL THEN
    user_username := 'user_' || SUBSTRING(NEW.id::text, 1, 8);
  END IF;
  
  -- Ensure full_name is not empty
  IF user_full_name = '' OR user_full_name IS NULL THEN
    user_full_name := 'User';
  END IF;
  
  -- Insert the profile
  INSERT INTO public.profiles (
    user_id,
    full_name,
    username,
    location_lat,
    location_lng,
    is_visible,
    visibility_radius
  )
  VALUES (
    NEW.id,
    user_full_name,
    user_username,
    NULL, -- Will be set during onboarding
    NULL, -- Will be set during onboarding
    true,
    5000 -- Default 5km radius
  );
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the user creation
    RAISE WARNING 'Error creating profile for user %: %', NEW.id, SQLERRM;
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.user_is_conversation_participant(p_conversation_id uuid, p_user_id uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
declare
  is_participant boolean := false;
begin
  -- Temporarily disable RLS to avoid recursion
  set local row_security = off;

  select exists (
    select 1
    from public.conversation_participants cp
    where cp.conversation_id = p_conversation_id
    and cp.user_id = p_user_id
  ) into is_participant;

  return is_participant;
end;
$function$
;

create policy "participants_select_realtime_bypass"
on "public"."conversation_participants"
as permissive
for select
to public
using ((is_realtime_internal() OR ((auth.role() = 'authenticated'::text) AND user_is_conversation_participant(conversation_id, auth.uid()))));


create policy "realtime_participants_service_bypass"
on "public"."conversation_participants"
as permissive
for select
to public
using (((auth.role() = 'service_role'::text) OR ((auth.role() = 'authenticated'::text) AND user_is_conversation_participant(conversation_id, auth.uid()))));


create policy "secure_participants_insert_blocked"
on "public"."conversation_participants"
as permissive
for insert
to public
with check (false);


create policy "conversations_select_realtime_bypass"
on "public"."conversations"
as permissive
for select
to public
using ((is_realtime_internal() OR ((auth.role() = 'authenticated'::text) AND (EXISTS ( SELECT 1
   FROM conversation_participants cp
  WHERE ((cp.conversation_id = conversations.id) AND (cp.user_id = auth.uid())))))));


create policy "realtime_conversations_service_bypass"
on "public"."conversations"
as permissive
for select
to public
using (((auth.role() = 'service_role'::text) OR ((auth.role() = 'authenticated'::text) AND (EXISTS ( SELECT 1
   FROM conversation_participants cp
  WHERE ((cp.conversation_id = conversations.id) AND (cp.user_id = auth.uid())))))));


create policy "secure_conversations_insert_blocked"
on "public"."conversations"
as permissive
for insert
to public
with check (false);


create policy "messages_select_realtime_bypass"
on "public"."messages"
as permissive
for select
to public
using ((is_realtime_internal() OR ((auth.role() = 'authenticated'::text) AND (EXISTS ( SELECT 1
   FROM conversation_participants cp
  WHERE ((cp.conversation_id = messages.conversation_id) AND (cp.user_id = auth.uid())))))));


create policy "realtime_service_bypass"
on "public"."messages"
as permissive
for select
to public
using (((auth.role() = 'service_role'::text) OR ((auth.role() = 'authenticated'::text) AND (EXISTS ( SELECT 1
   FROM conversation_participants cp
  WHERE ((cp.conversation_id = messages.conversation_id) AND (cp.user_id = auth.uid())))))));


create policy "secure_messages_insert"
on "public"."messages"
as permissive
for insert
to public
with check (((sender_id = auth.uid()) AND (conversation_id IS NOT NULL) AND (EXISTS ( SELECT 1
   FROM conversation_participants cp_self
  WHERE ((cp_self.conversation_id = messages.conversation_id) AND (cp_self.user_id = auth.uid())))) AND (EXISTS ( SELECT 1
   FROM ((conversation_participants cp_self
     JOIN conversation_participants cp_other ON (((cp_other.conversation_id = cp_self.conversation_id) AND (cp_other.user_id <> cp_self.user_id))))
     JOIN connections c ON ((((c.requester_id = cp_self.user_id) AND (c.receiver_id = cp_other.user_id)) OR ((c.requester_id = cp_other.user_id) AND (c.receiver_id = cp_self.user_id)))))
  WHERE ((cp_self.conversation_id = messages.conversation_id) AND (cp_self.user_id = auth.uid()) AND (c.status = 'accepted'::text))))));


create policy "secure_messages_update"
on "public"."messages"
as permissive
for update
to public
using (((EXISTS ( SELECT 1
   FROM conversation_participants cp
  WHERE ((cp.conversation_id = messages.conversation_id) AND (cp.user_id = auth.uid())))) AND (receiver_id = auth.uid())));



