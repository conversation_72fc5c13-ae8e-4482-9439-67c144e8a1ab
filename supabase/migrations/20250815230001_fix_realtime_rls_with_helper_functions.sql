-- Fix realtime + RLS compatibility in a minimal, safe way
-- 1) Provide helpers used by RLS policies without recursive RLS lookups
-- 2) Ensure service_role can read for replication/broadcast only

-- Helper: check whether a user is a participant in a conversation
create or replace function public.user_is_conversation_participant(p_conversation_id uuid, p_user_id uuid)
returns boolean
language plpgsql
security definer
set search_path = public
as $$
declare
  is_participant boolean := false;
begin
  set local row_security = off;
  select exists (
    select 1 from public.conversation_participants cp
    where cp.conversation_id = p_conversation_id
      and cp.user_id = p_user_id
  ) into is_participant;
  return is_participant;
end;
$$;

-- Helper: identify Supabase internal realtime usage (service role)
create or replace function public.is_realtime_internal()
returns boolean
language sql
stable
as $$ select auth.role() = 'service_role' $$;

-- Messages: allow service_role to read for replication; enforce participant check for users
drop policy if exists messages_select_realtime_bypass on public.messages;
drop policy if exists realtime_service_bypass on public.messages;
create policy messages_select_realtime_bypass on public.messages
for select using (
  public.is_realtime_internal() OR public.user_is_conversation_participant(conversation_id, auth.uid())
);

-- Conversations
drop policy if exists conversations_select_realtime_bypass on public.conversations;
drop policy if exists realtime_conversations_service_bypass on public.conversations;
create policy conversations_select_realtime_bypass on public.conversations
for select using (
  public.is_realtime_internal() OR public.user_is_conversation_participant(id, auth.uid())
);

-- Participants (only needed for some UI queries)
drop policy if exists participants_select_realtime_bypass on public.conversation_participants;
drop policy if exists realtime_participants_service_bypass on public.conversation_participants;
create policy participants_select_realtime_bypass on public.conversation_participants
for select using (
  public.is_realtime_internal() OR public.user_is_conversation_participant(conversation_id, auth.uid())
);

-- Grants (read only) for service_role used by Realtime
grant usage on schema public to service_role;
grant select on public.messages to service_role;
grant select on public.conversations to service_role;
grant select on public.conversation_participants to service_role;

-- Ensure replica identity and publication
alter table public.messages replica identity full;
alter table public.conversations replica identity full;
alter table public.conversation_participants replica identity full;

do $$
begin
  begin
    alter publication supabase_realtime add table public.messages;
  exception when duplicate_object then null; end;
  begin
    alter publication supabase_realtime add table public.conversations;
  exception when duplicate_object then null; end;
  begin
    alter publication supabase_realtime add table public.conversation_participants;
  exception when duplicate_object then null; end;
end $$;