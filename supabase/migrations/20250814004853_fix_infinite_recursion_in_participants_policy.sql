-- Fix infinite recursion in conversation_participants policy
--
-- PROBLEM: The previous policy created infinite recursion by querying the same table
-- it was protecting within the policy condition.
--
-- SOLUTION: Use a SECURITY DEFINER function to check participation, which bypasses RLS
-- and avoids the recursion issue.

-- First, drop the problematic policy
drop policy if exists cp_select_if_conversation_participant on public.conversation_participants;

-- Create a helper function that can check participation without RLS
create or replace function public.user_is_conversation_participant(p_conversation_id uuid, p_user_id uuid)
returns boolean
language plpgsql
security definer
set search_path = public
as $$
declare
  is_participant boolean := false;
begin
  -- Temporarily disable <PERSON><PERSON> to avoid recursion
  set local row_security = off;

  select exists (
    select 1
    from public.conversation_participants cp
    where cp.conversation_id = p_conversation_id
      and cp.user_id = p_user_id
  ) into is_participant;

  return is_participant;
end;
$$;

-- Create new policy using the helper function
create policy cp_select_if_conversation_participant on public.conversation_participants
for select using (
  public.user_is_conversation_participant(conversation_id, auth.uid())
);

-- This approach:
-- 1. Avoids infinite recursion by using a SECURITY DEFINER function
-- 2. Still allows users to see all participants for their conversations
-- 3. Maintains security by only showing participants for conversations the user is in
-- 4. Enables the messages insert policy to work correctly