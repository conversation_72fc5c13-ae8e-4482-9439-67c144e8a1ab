-- Critical Database Fixes for Nearby Connect Feature
-- Resolves column ambiguity, schema mismatches, and production-ready error handling

-- 1. Fix update_user_location function - resolve column ambiguity
create or replace function public.update_user_location(
  lat decimal(10, 8),
  lng decimal(11, 8),
  accuracy float default null
)
returns json
language plpgsql
security definer
set search_path = public
as $$
declare
  current_user_id uuid := auth.uid();
  last_update timestamptz;
  result json;
  rows_affected integer;
begin
  -- Authentication check
  if current_user_id is null then
    raise exception 'Authentication required' using errcode = 'UNAUTHENTICATED';
  end if;

  -- Input validation
  if lat is null or lng is null then
    raise exception 'Latitude and longitude are required' using errcode = 'INVALID_PARAMETER_VALUE';
  end if;

  if lat < -90 or lat > 90 then
    raise exception 'Latitude must be between -90 and 90 degrees' using errcode = 'INVALID_PARAMETER_VALUE';
  end if;

  if lng < -180 or lng > 180 then
    raise exception 'Longitude must be between -180 and 180 degrees' using errcode = 'INVALID_PARAMETER_VALUE';
  end if;

  if accuracy is not null and accuracy < 0 then
    raise exception 'Accuracy must be non-negative' using errcode = 'INVALID_PARAMETER_VALUE';
  end if;

  -- Rate limiting check with proper table aliasing
  select p.location_updated_at into last_update
  from public.profiles p
  where p.id = current_user_id;

  if last_update is not null and last_update > now() - interval '30 seconds' then
    raise exception 'Location updates are rate limited to once per 30 seconds' using errcode = 'TOO_MANY_REQUESTS';
  end if;

  -- Update location with proper table aliasing
  update public.profiles p
  set
    latitude = lat,
    longitude = lng,
    location_updated_at = now(),
    location_accuracy = accuracy,
    last_seen = now(),
    online_status = true
  where p.id = current_user_id;

  get diagnostics rows_affected = row_count;

  if rows_affected = 0 then
    raise exception 'Profile not found or update failed' using errcode = 'NO_DATA_FOUND';
  end if;

  -- Return success response
  select json_build_object(
    'success', true,
    'latitude', lat,
    'longitude', lng,
    'accuracy', accuracy,
    'updated_at', now(),
    'message', 'Location updated successfully'
  ) into result;

  return result;
end;
$$;

-- 2. Fix find_nearby_users function - correct column names and improve error handling
create or replace function public.find_nearby_users(
  user_lat decimal default null,
  user_lng decimal default null,
  radius_km integer default 5,
  interest_filter text[] default null,
  include_offline boolean default true
)
returns table (
  id uuid,
  name text,
  avatar_url text,
  bio text,
  interests text[],
  latitude decimal,
  longitude decimal,
  distance_km decimal,
  online_status boolean,
  last_seen timestamptz,
  connection_status text,
  shared_interests text[]
)
language plpgsql
security definer
set search_path = public
as $$
declare
  current_user_id uuid := auth.uid();
  user_location record;
begin
  -- Authentication check
  if current_user_id is null then
    raise exception 'Authentication required' using errcode = 'UNAUTHENTICATED';
  end if;

  -- Input validation
  if radius_km is null or radius_km <= 0 or radius_km > 100 then
    raise exception 'Radius must be between 1 and 100 kilometers' using errcode = 'INVALID_PARAMETER_VALUE';
  end if;

  -- Get user's location if not provided
  if user_lat is null or user_lng is null then
    select p.latitude, p.longitude into user_location
    from public.profiles p
    where p.id = current_user_id;

    if user_location.latitude is null or user_location.longitude is null then
      raise exception 'User location not available. Please update your location first.' using errcode = 'PRECONDITION_FAILED';
    end if;

    user_lat := user_location.latitude;
    user_lng := user_location.longitude;
  end if;

  -- Validate provided coordinates
  if user_lat < -90 or user_lat > 90 or user_lng < -180 or user_lng > 180 then
    raise exception 'Invalid coordinates provided' using errcode = 'INVALID_PARAMETER_VALUE';
  end if;

  return query
  select
    p.id,
    p.full_name as name,  -- Fix: use full_name from actual schema
    p.avatar_url,
    p.bio,
    p.interests,
    p.latitude,
    p.longitude,
    public.calculate_distance(user_lat, user_lng, p.latitude, p.longitude) as distance_km,
    p.online_status,
    p.last_seen,
    coalesce(c.status, 'none') as connection_status,
    case
      when interest_filter is not null and array_length(interest_filter, 1) > 0 then
        array(select unnest(p.interests) intersect select unnest(interest_filter))
      else
        array[]::text[]
    end as shared_interests
  from public.profiles p
  left join public.connections c on (
    (c.requester_id = current_user_id and c.receiver_id = p.id) or
    (c.requester_id = p.id and c.receiver_id = current_user_id)
  )
  where
    p.id != current_user_id
    and p.latitude is not null
    and p.longitude is not null
    and p.visibility_status = 'visible'
    and (include_offline = true or p.online_status = true)
    and public.calculate_distance(user_lat, user_lng, p.latitude, p.longitude) <= radius_km
    and (
      interest_filter is null
      or array_length(interest_filter, 1) is null
      or p.interests && interest_filter  -- Array overlap operator
    )
  order by distance_km asc
  limit 100; -- Reasonable limit for map display
end;
$$;

-- 3. Fix toggle_visibility_status function - improve error handling
create or replace function public.toggle_visibility_status(new_status visibility_status)
returns json
language plpgsql
security definer
set search_path = public
as $$
declare
  current_user_id uuid := auth.uid();
  result json;
  rows_affected integer;
begin
  -- Authentication check
  if current_user_id is null then
    raise exception 'Authentication required' using errcode = 'UNAUTHENTICATED';
  end if;

  -- Input validation
  if new_status is null then
    raise exception 'Visibility status is required' using errcode = 'INVALID_PARAMETER_VALUE';
  end if;

  -- Update visibility status with proper table aliasing
  update public.profiles p
  set visibility_status = new_status
  where p.id = current_user_id;

  get diagnostics rows_affected = row_count;

  if rows_affected = 0 then
    raise exception 'Profile not found or update failed' using errcode = 'NO_DATA_FOUND';
  end if;

  select json_build_object(
    'success', true,
    'visibility_status', new_status,
    'message', 'Visibility status updated successfully'
  ) into result;

  return result;
end;
$$;

-- 4. Fix update_user_interests function - improve validation and error handling
create or replace function public.update_user_interests(new_interests text[])
returns json
language plpgsql
security definer
set search_path = public
as $$
declare
  current_user_id uuid := auth.uid();
  result json;
  rows_affected integer;
  cleaned_interests text[];
begin
  -- Authentication check
  if current_user_id is null then
    raise exception 'Authentication required' using errcode = 'UNAUTHENTICATED';
  end if;

  -- Handle null input
  if new_interests is null then
    new_interests := array[]::text[];
  end if;

  -- Validate interests array length
  if array_length(new_interests, 1) > 10 then
    raise exception 'Maximum 10 interests allowed' using errcode = 'INVALID_PARAMETER_VALUE';
  end if;

  -- Clean and validate each interest
  cleaned_interests := array[]::text[];
  for i in 1..coalesce(array_length(new_interests, 1), 0) loop
    declare
      cleaned_interest text := trim(new_interests[i]);
    begin
      if length(cleaned_interest) = 0 then
        continue; -- Skip empty interests
      end if;

      if length(cleaned_interest) > 50 then
        raise exception 'Each interest must be 50 characters or less' using errcode = 'INVALID_PARAMETER_VALUE';
      end if;

      -- Add to cleaned array if not already present
      if not (cleaned_interest = any(cleaned_interests)) then
        cleaned_interests := array_append(cleaned_interests, cleaned_interest);
      end if;
    end;
  end loop;

  -- Update interests with proper table aliasing
  update public.profiles p
  set interests = cleaned_interests
  where p.id = current_user_id;

  get diagnostics rows_affected = row_count;

  if rows_affected = 0 then
    raise exception 'Profile not found or update failed' using errcode = 'NO_DATA_FOUND';
  end if;

  select json_build_object(
    'success', true,
    'interests', cleaned_interests,
    'message', 'Interests updated successfully'
  ) into result;

  return result;
end;
$$;

-- 5. Fix update_online_presence function - improve error handling
create or replace function public.update_online_presence(is_online boolean default true)
returns json
language plpgsql
security definer
set search_path = public
as $$
declare
  current_user_id uuid := auth.uid();
  result json;
  rows_affected integer;
begin
  -- Authentication check
  if current_user_id is null then
    raise exception 'Authentication required' using errcode = 'UNAUTHENTICATED';
  end if;

  -- Input validation
  if is_online is null then
    is_online := true;
  end if;

  -- Update online presence with proper table aliasing
  update public.profiles p
  set
    online_status = is_online,
    last_seen = now()
  where p.id = current_user_id;

  get diagnostics rows_affected = row_count;

  if rows_affected = 0 then
    raise exception 'Profile not found or update failed' using errcode = 'NO_DATA_FOUND';
  end if;

  select json_build_object(
    'success', true,
    'online_status', is_online,
    'last_seen', now(),
    'message', 'Online presence updated successfully'
  ) into result;

  return result;
end;
$$;

-- 6. Improve calculate_distance function with better error handling
create or replace function public.calculate_distance(
  lat1 decimal, lng1 decimal,
  lat2 decimal, lng2 decimal
)
returns decimal
language plpgsql
immutable
as $$
declare
  r decimal := 6371; -- Earth's radius in kilometers
  dlat decimal;
  dlng decimal;
  a decimal;
  c decimal;
begin
  -- Input validation
  if lat1 is null or lng1 is null or lat2 is null or lng2 is null then
    return null;
  end if;

  if lat1 < -90 or lat1 > 90 or lat2 < -90 or lat2 > 90 then
    raise exception 'Invalid latitude values' using errcode = 'INVALID_PARAMETER_VALUE';
  end if;

  if lng1 < -180 or lng1 > 180 or lng2 < -180 or lng2 > 180 then
    raise exception 'Invalid longitude values' using errcode = 'INVALID_PARAMETER_VALUE';
  end if;

  -- Haversine formula
  dlat := radians(lat2 - lat1);
  dlng := radians(lng2 - lng1);

  a := sin(dlat/2) * sin(dlat/2) +
       cos(radians(lat1)) * cos(radians(lat2)) *
       sin(dlng/2) * sin(dlng/2);

  c := 2 * atan2(sqrt(a), sqrt(1-a));

  return round((r * c)::numeric, 3); -- Round to 3 decimal places
end;
$$;