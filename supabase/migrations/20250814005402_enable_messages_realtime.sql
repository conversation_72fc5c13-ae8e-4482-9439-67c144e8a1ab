-- Enable realtime for messages table
-- This ensures that real-time subscriptions work for message inserts

-- Add messages table to the realtime publication
alter publication supabase_realtime add table public.messages;

-- Also add conversations and conversation_participants for completeness
alter publication supabase_realtime add table public.conversations;
alter publication supabase_realtime add table public.conversation_participants;

-- This will enable real-time subscriptions for:
-- 1. New messages being inserted
-- 2. New conversations being created
-- 3. New participants being added to conversations