-- Drop the old function if it exists (required to change return type)
DROP FUNCTION IF EXISTS public.find_nearby_users(numeric, numeric, integer);

-- Create the new version of find_nearby_users with distance in meters
CREATE OR REPLACE FUNCTION public.find_nearby_users(
  user_lat numeric,
  user_lng numeric,
  radius_m integer DEFAULT 5000
)
RETURNS TABLE(
  id uuid,
  user_id uuid,
  full_name text,
  username text,
  bio text,
  interests text[],
  is_business boolean,
  distance_m numeric
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id,
    p.user_id,
    p.full_name,
    p.username,
    p.bio,
    p.interests,
    p.is_business,
    ROUND(
      (
        6371000 * acos(
          cos(radians(user_lat)) * 
          cos(radians(p.location_lat)) * 
          cos(radians(p.location_lng) - radians(user_lng)) + 
          sin(radians(user_lat)) * 
          sin(radians(p.location_lat))
        )
      )::numeric, 2
    ) as distance_m
  FROM public.profiles p
  WHERE 
    p.user_id != auth.uid() -- Exclude current user
    AND p.is_visible = true
    AND p.location_lat IS NOT NULL 
    AND p.location_lng IS NOT NULL
    AND (
      6371000 * acos(
        cos(radians(user_lat)) * 
        cos(radians(p.location_lat)) * 
        cos(radians(p.location_lng) - radians(user_lng)) + 
        sin(radians(user_lat)) * 
        sin(radians(p.location_lat))
      )
    ) <= COALESCE(p.visibility_radius, radius_m)
  ORDER BY distance_m ASC
  LIMIT 50;
END;
$$;