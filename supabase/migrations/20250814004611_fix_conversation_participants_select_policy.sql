-- Fix conversation_participants SELECT policy to allow message insertion
--
-- PROBLEM: The current cp_select_own policy only allows users to see their own participant row.
-- This breaks the messages insert policy because it needs to see BOTH participants to verify
-- the accepted connection between them.
--
-- SOLUTION: Allow users to see ALL participant rows for conversations they are part of.
-- This maintains security (users only see participants for their own conversations)
-- while enabling the messages policy to work correctly.

-- Drop the restrictive policy that only allows seeing own participant row
drop policy if exists cp_select_own on public.conversation_participants;

-- Create new policy that allows seeing all participants for conversations the user is part of
create policy cp_select_if_conversation_participant on public.conversation_participants
for select using (
  exists (
    select 1
    from public.conversation_participants cp2
    where cp2.conversation_id = conversation_participants.conversation_id
      and cp2.user_id = auth.uid()
  )
);

-- This policy allows a user to:
-- 1. See their own participant row (same as before)
-- 2. See the other participant's row for conversations they're in
-- 3. NOT see participant rows for conversations they're not part of
--
-- This enables the messages insert policy to work because it can now see
-- both cp_self and cp_other when checking the accepted connection requirement.