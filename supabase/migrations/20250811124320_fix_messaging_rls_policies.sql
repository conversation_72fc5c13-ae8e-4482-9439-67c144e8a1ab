-- Fix messaging RLS policies and RPC function
-- This migration fixes the critical issue preventing messaging from working

-- CRITICAL FIX: The main issue is that the get_or_create_conversation RPC function
-- cannot create conversation participants due to the restrictive RLS policy.
-- We need to add "set local row_security = off" to bypass R<PERSON> within the function.

-- 1) Ensure RLS is enabled on all conversation-related tables
alter table public.conversations enable row level security;
alter table public.conversation_participants enable row level security;
alter table public.messages enable row level security;

-- 2) Drop only the old conflicting message policies that might interfere
drop policy if exists "Users can send messages" on public.messages;
drop policy if exists "Users can view their messages" on public.messages;
drop policy if exists "Users can update received messages" on public.messages;

-- 3) The CRITICAL FIX: Update the get_or_create_conversation RPC function
-- to bypass RLS when creating participants
create or replace function public.get_or_create_conversation(partner_id uuid)
returns uuid
language plpgsql
security definer
set search_path = public
as $$
declare
  me uuid := auth.uid();
  conv_id uuid;
begin
  if me is null then
    raise exception 'Not authenticated';
  end if;

  -- Ensure accepted connection exists
  if not exists (
    select 1 from public.connections c
    where c.status = 'accepted'
      and (
        (c.requester_id = me and c.receiver_id = partner_id) or
        (c.requester_id = partner_id and c.receiver_id = me)
      )
  ) then
    raise exception 'No accepted connection between users';
  end if;

  -- Try to find existing conversation for these two users
  select c.id into conv_id
  from public.conversations c
  where exists (
          select 1 from public.conversation_participants p
          where p.conversation_id = c.id and p.user_id = me
        )
    and exists (
          select 1 from public.conversation_participants p
          where p.conversation_id = c.id and p.user_id = partner_id
        )
  limit 1;

  if conv_id is not null then
    return conv_id;
  end if;

  -- CRITICAL FIX: Temporarily disable RLS for this function to allow participant creation
  -- This is the key fix that allows the RPC function to work despite restrictive RLS policies
  set local row_security = off;

  -- Create new conversation and add both participants
  insert into public.conversations default values returning id into conv_id;
  insert into public.conversation_participants (conversation_id, user_id)
  values (conv_id, me), (conv_id, partner_id);

  return conv_id;
end;
$$;

-- 4) Ensure the messages table has the correct policies
-- Only create policies if they don't already exist to avoid conflicts

-- Create conversation-based message selection policy if it doesn't exist
do $$
begin
  if not exists (
    select 1 from pg_policies
    where schemaname = 'public'
    and tablename = 'messages'
    and policyname = 'msg_select_if_participant'
  ) then
    create policy msg_select_if_participant on public.messages
    for select using (
      exists (
        select 1 from public.conversation_participants cp
        where cp.conversation_id = messages.conversation_id and cp.user_id = auth.uid()
      )
    );
  end if;
end $$;

-- Create conversation-based message insertion policy if it doesn't exist
do $$
begin
  if not exists (
    select 1 from pg_policies
    where schemaname = 'public'
    and tablename = 'messages'
    and policyname = 'msg_insert_if_participant_and_connected'
  ) then
    create policy msg_insert_if_participant_and_connected on public.messages
    for insert with check (
      sender_id = auth.uid()
      and conversation_id is not null
      and exists (
        select 1 from public.conversation_participants cp_self
        where cp_self.conversation_id = messages.conversation_id
          and cp_self.user_id = auth.uid()
      )
      and exists (
        select 1
        from public.conversation_participants cp_self
        join public.conversation_participants cp_other
          on cp_other.conversation_id = cp_self.conversation_id
         and cp_other.user_id <> cp_self.user_id
        join public.connections c
          on (
            (c.requester_id = cp_self.user_id and c.receiver_id = cp_other.user_id) or
            (c.requester_id = cp_other.user_id and c.receiver_id = cp_self.user_id)
          )
        where cp_self.conversation_id = messages.conversation_id
          and cp_self.user_id = auth.uid()
          and c.status = 'accepted'
      )
    );
  end if;
end $$;