-- TEMPORARY: Disable <PERSON><PERSON> to restore WhatsApp-like real-time messaging
-- This is the exact state that was working before we added RLS
-- We'll re-enable RLS with proper policies once real-time is confirmed working

-- Step 1: Disable <PERSON><PERSON> on messaging tables (this was the working state)
alter table public.messages disable row level security;
alter table public.conversations disable row level security;
alter table public.conversation_participants disable row level security;

-- Step 2: Drop all the RLS policies that are blocking real-time
drop policy if exists messages_select_realtime_bypass on public.messages;
drop policy if exists conversations_select_realtime_bypass on public.conversations;
drop policy if exists participants_select_realtime_bypass on public.conversation_participants;

drop policy if exists msg_select_if_participant on public.messages;
drop policy if exists msg_insert_if_participant_and_connected on public.messages;
drop policy if exists msg_update_if_participant_receiver on public.messages;

drop policy if exists conv_select_if_participant on public.conversations;
drop policy if exists conv_insert_none on public.conversations;

drop policy if exists cp_select_own on public.conversation_participants;
drop policy if exists cp_insert_none on public.conversation_participants;

-- Step 3: Restore the permissive grants that worked with real-time
grant usage on schema public to anon, authenticated;
grant select on public.messages to anon, authenticated;
grant select on public.conversations to anon, authenticated;
grant select on public.conversation_participants to anon, authenticated;
grant insert on public.messages to authenticated;

-- Step 4: Ensure real-time configuration is correct
alter table public.messages replica identity full;
alter table public.conversations replica identity full;
alter table public.conversation_participants replica identity full;

-- Step 5: Ensure tables are in the real-time publication
do $$
begin
  begin
    alter publication supabase_realtime add table public.messages;
  exception when duplicate_object then null;
  end;

  begin
    alter publication supabase_realtime add table public.conversations;
  exception when duplicate_object then null;
  end;

  begin
    alter publication supabase_realtime add table public.conversation_participants;
  exception when duplicate_object then null;
  end;
end $$;

-- NOTE: This temporarily removes security to restore WhatsApp-like functionality
-- Security will be re-implemented once real-time is confirmed working
-- The application-level logic still provides some protection:
-- 1. Users can only send messages in conversations they participate in (RPC function enforces this)
-- 2. Real-time subscriptions are filtered by conversation_id
-- 3. Frontend only shows conversations the user is part of